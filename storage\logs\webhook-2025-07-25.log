[2025-07-25 05:17:15] local.INFO: Starting webhook dispatch for action: crm.lead_created {"timestamp":"2025-07-25T05:17:15.562144Z","source":"crm_webhook_system","action":"crm.lead_created","user_id":84,"entity_type":"Lead","entity_id":12,"status":"dispatching"} 
[2025-07-25 05:17:19] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2024 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-25T05:17:19.150463Z","source":"crm_webhook_system","action":"crm.lead_created","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":3554.0,"user_id":84,"entity_id":12,"entity_type":"Lead","request_payload":{"action":"crm.lead_created","timestamp":"2025-07-25T05:17:15.596913Z","data":{"name":"Mr Rajkumer Sarkar","email":"<EMAIL>","phone":"+918654589658","subject":"Booking bot","user_id":"88","pipeline_id":27,"stage_id":105,"created_by":84,"date":"2025-07-25","next_follow_up_date":null,"updated_at":"2025-07-25T05:17:15.000000Z","created_at":"2025-07-25T05:17:15.000000Z","id":12,"stage":{"id":105,"name":"New","pipeline_id":27,"created_by":84,"order":0,"created_at":"2025-07-21T10:19:26.000000Z","updated_at":"2025-07-21T10:19:26.000000Z"},"pipeline":{"id":27,"name":"Default Pipeline","created_by":84,"created_at":"2025-07-21T10:19:26.000000Z","updated_at":"2025-07-21T10:19:26.000000Z"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2024 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-25 05:17:21] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2030 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-25T05:17:21.184108Z","source":"crm_webhook_system","action":"crm.lead_created","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2032.0,"user_id":84,"entity_id":12,"entity_type":"Lead","request_payload":{"action":"crm.lead_created","timestamp":"2025-07-25T05:17:19.151664Z","data":{"name":"Mr Rajkumer Sarkar","email":"<EMAIL>","phone":"+918654589658","subject":"Booking bot","user_id":"88","pipeline_id":27,"stage_id":105,"created_by":84,"date":"2025-07-25","next_follow_up_date":null,"updated_at":"2025-07-25T05:17:15.000000Z","created_at":"2025-07-25T05:17:15.000000Z","id":12,"stage":{"id":105,"name":"New","pipeline_id":27,"created_by":84,"order":0,"created_at":"2025-07-21T10:19:26.000000Z","updated_at":"2025-07-21T10:19:26.000000Z"},"pipeline":{"id":27,"name":"Default Pipeline","created_by":84,"created_at":"2025-07-21T10:19:26.000000Z","updated_at":"2025-07-21T10:19:26.000000Z"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2030 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-25 05:17:21] local.WARNING: Webhook dispatch completed for action: crm.lead_created. Success: 0, Failed: 2 {"timestamp":"2025-07-25T05:17:21.185837Z","source":"crm_webhook_system","action":"crm.lead_created","user_id":84,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2024 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2030 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
[2025-07-25 05:17:37] local.INFO: Starting webhook dispatch for action: crm.lead_stage_changed {"timestamp":"2025-07-25T05:17:37.001366Z","source":"crm_webhook_system","action":"crm.lead_stage_changed","user_id":84,"entity_type":"Lead","entity_id":12,"status":"dispatching"} 
[2025-07-25 05:17:39] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2048 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-25T05:17:39.096397Z","source":"crm_webhook_system","action":"crm.lead_stage_changed","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2086.0,"user_id":84,"entity_id":12,"entity_type":"Lead","request_payload":{"action":"crm.lead_stage_changed","timestamp":"2025-07-25T05:17:37.010377Z","data":{"id":12,"name":"Mr Rajkumer Sarkar","email":"<EMAIL>","phone":"+918654589658","subject":"Booking bot","user_id":88,"pipeline_id":27,"stage_id":105,"sources":[],"products":[],"notes":null,"labels":[],"order":0,"created_by":84,"is_active":1,"is_converted":0,"date":"2025-07-25","next_follow_up_date":null,"created_at":"2025-07-25T05:17:15.000000Z","updated_at":"2025-07-25T05:17:15.000000Z","stage":{"id":105,"name":"New","pipeline_id":27,"created_by":84,"order":0,"created_at":"2025-07-21T10:19:26.000000Z","updated_at":"2025-07-21T10:19:26.000000Z"},"users":[{"id":84,"name":"parichay  Bot flow","email":"<EMAIL>","email_verified_at":"2025-07-21T06:08:10.000000Z","plan":16,"plan_expire_date":"2026-07-23","requested_plan":0,"trial_plan":0,"trial_expire_date":null,"type":"company","system_admin_company_id":null,"company_name":null,"company_description":null,"module_permissions":{"crm":["create lead","create deal","create form builder","create contract","create pipeline","create stage","create source","create label","view crm dashboard","view lead","view deal","view form builder","view contract","view pipeline","view stage","view source","view label","delete lead","delete deal","delete form builder","delete contract","delete pipeline","delete stage","delete source","delete label","manage lead","edit lead","manage deal","edit deal","manage form builder","edit form builder","manage contract","edit contract","manage pipeline","edit pipeline","manage stage","edit stage","manage source","edit source","manage label","edit label"],"hrm":["create employee","create set salary","create pay slip","create leave","create attendance","create training","create award","create branch","create department","create designation","create document type","view hrm dashboard","view employee","view set salary","view pay slip","view leave","view attendance","view training","view award","view branch","view department","view designation","view document type","delete employee","delete set salary","delete pay slip","delete leave","delete attendance","delete training","delete award","delete branch","delete department","delete designation","delete document type","manage employee","edit employee","manage set salary","edit set salary","manage pay slip","edit pay slip","manage leave","edit leave","manage attendance","edit attendance","manage training","edit training","manage award","edit award","manage branch","edit branch","manage department","edit department","manage designation","edit designation","manage document type","edit document type"],"account":["create customer","create vender","create invoice","create bill","create revenue","create payment","create proposal","create goal","create credit note","create debit note","create bank account","create bank transfer","create transaction","create chart of account","create journal entry","create assets","create constant custom field","view account dashboard","view customer","view vender","view invoice","view bill","view revenue","view payment","view proposal","view goal","view credit note","view debit note","view bank account","view bank transfer","view transaction","view chart of account","view journal entry","view assets","view constant custom field","view report","delete customer","delete vender","delete invoice","delete bill","delete revenue","delete payment","delete proposal","delete goal","delete credit note","delete debit note","delete bank account","delete bank transfer","delete transaction","delete chart of account","delete journal entry","delete assets","delete constant custom field","manage customer","edit customer","manage vender","edit vender","manage invoice","edit invoice","manage bill","edit bill","manage revenue","edit revenue","manage payment","edit payment","manage proposal","edit proposal","manage goal","edit goal","manage credit note","edit credit note","manage debit note","edit debit note","manage bank account","edit bank account","manage bank transfer","edit bank transfer","manage transaction","edit transaction","manage chart of account","edit chart of account","manage journal entry","edit journal entry","manage assets","edit assets","manage constant custom field","edit constant custom field","manage report"],"project":["create project","create project task","create timesheet","create bug report","create milestone","create project stage","create project task stage","create project expense","create activity","create bug status","view project dashboard","view project","view project task","view timesheet","view bug report","view milestone","view project stage","view project task stage","view project expense","view activity","view bug status","delete project","delete project task","delete timesheet","delete bug report","delete milestone","delete project stage","delete project task stage","delete project expense","delete activity","delete bug status","manage project","edit project","manage project task","edit project task","manage timesheet","edit timesheet","manage bug report","edit bug report","manage milestone","edit milestone","manage project stage","edit project stage","manage project task stage","edit project task stage","manage project expense","edit project expense","manage activity","edit activity","manage bug status","edit bug status"],"pos":["create warehouse","create purchase","create quotation","create pos","create barcode","create product","create product category","create product unit","view pos dashboard","view warehouse","view purchase","view quotation","view pos","view product","view product category","view product unit","delete warehouse","delete purchase","delete quotation","delete pos","delete product","delete product category","delete product unit","manage warehouse","edit warehouse","manage purchase","edit purchase","manage quotation","edit quotation","manage pos","edit pos","manage product","edit product","manage product category","edit product category","manage product unit","edit product unit"],"support":["create support","view support dashboard","view support","delete support","manage support","edit support","reply support"],"user_management":["create user","create client","view user","view client","delete user","delete client","manage user","edit user","manage client","edit client"],"booking":["create booking","create appointment","create appointment booking","create calendar event","view booking dashboard","view booking","show booking","view appointment","show appointment","view appointment booking","show appointment booking","view calendar event","show calendar event","delete booking","delete appointment","delete appointment booking","delete calendar event","manage booking","edit booking","manage appointment","edit appointment","manage appointment booking","edit appointment booking","manage calendar event","edit calendar event"],"omx_flow":["access omx flow","whatsapp_flows","whatsapp_orders","campaigns","templates","chatbot"],"personal_tasks":["create personal task","create personal task comment","create personal task file","create personal task checklist","view personal task","delete personal task","delete personal task comment","delete personal task file","delete personal task checklist","manage personal task","edit personal task","edit personal task comment","edit personal task checklist","manage personal task time tracking"],"automatish":["access automatish"]},"storage_limit":0.0,"avatar":"","messenger_color":"#2180f3","lang":"en","default_pipeline":null,"active_status":0,"delete_status":1,"mode":"light","dark_mode":0,"is_disable":1,"is_enable_login":1,"is_active":1,"referral_code":0,"used_referral_code":0,"commission_amount":0,"last_login_at":null,"created_by":7,"created_at":"2025-07-21T06:08:10.000000Z","updated_at":"2025-07-23T13:27:13.000000Z","is_email_verified":0,"profile":"http://localhost:8000/storage/avatar.png","pivot":{"lead_id":12,"user_id":84}},{"id":88,"name":"Raj Sharma","email":"<EMAIL>","email_verified_at":"2025-07-25T05:16:21.000000Z","plan":null,"plan_expire_date":null,"requested_plan":0,"trial_plan":0,"trial_expire_date":null,"type":"employee","system_admin_company_id":null,"company_name":null,"company_description":null,"module_permissions":{"crm":["create lead","create deal","create form builder","create contract","create pipeline","create stage","create source","create label","view crm dashboard","view lead","view deal","view form builder","view contract","view pipeline","view stage","view source","view label","delete lead","delete deal","delete form builder","delete contract","delete pipeline","delete stage","delete source","delete label","manage lead","edit lead","manage deal","edit deal","manage form builder","edit form builder","manage contract","edit contract","manage pipeline","edit pipeline","manage stage","edit stage","manage source","edit source","manage label","edit label"],"hrm":["create employee","create set salary","create pay slip","create leave","create attendance","create training","create award","create branch","create department","create designation","create document type","view hrm dashboard","view employee","view set salary","view pay slip","view leave","view attendance","view training","view award","view branch","view department","view designation","view document type","delete employee","delete set salary","delete pay slip","delete leave","delete attendance","delete training","delete award","delete branch","delete department","delete designation","delete document type","manage employee","edit employee","manage set salary","edit set salary","manage pay slip","edit pay slip","manage leave","edit leave","manage attendance","edit attendance","manage training","edit training","manage award","edit award","manage branch","edit branch","manage department","edit department","manage designation","edit designation","manage document type","edit document type"],"account":["create customer","create vender","create invoice","create bill","create revenue","create payment","create proposal","create goal","create credit note","create debit note","create bank account","create bank transfer","create transaction","create chart of account","create journal entry","create assets","create constant custom field","view account dashboard","view customer","view vender","view invoice","view bill","view revenue","view payment","view proposal","view goal","view credit note","view debit note","view bank account","view bank transfer","view transaction","view chart of account","view journal entry","view assets","view constant custom field","view report","delete customer","delete vender","delete invoice","delete bill","delete revenue","delete payment","delete proposal","delete goal","delete credit note","delete debit note","delete bank account","delete bank transfer","delete transaction","delete chart of account","delete journal entry","delete assets","delete constant custom field","manage customer","edit customer","manage vender","edit vender","manage invoice","edit invoice","manage bill","edit bill","manage revenue","edit revenue","manage payment","edit payment","manage proposal","edit proposal","manage goal","edit goal","manage credit note","edit credit note","manage debit note","edit debit note","manage bank account","edit bank account","manage bank transfer","edit bank transfer","manage transaction","edit transaction","manage chart of account","edit chart of account","manage journal entry","edit journal entry","manage assets","edit assets","manage constant custom field","edit constant custom field","manage report"],"project":["create project","create project task","create timesheet","create bug report","create milestone","create project stage","create project task stage","create project expense","create activity","create bug status","view project dashboard","view project","view project task","view timesheet","view bug report","view milestone","view project stage","view project task stage","view project expense","view activity","view bug status","delete project","delete project task","delete timesheet","delete bug report","delete milestone","delete project stage","delete project task stage","delete project expense","delete activity","delete bug status","manage project","edit project","manage project task","edit project task","manage timesheet","edit timesheet","manage bug report","edit bug report","manage milestone","edit milestone","manage project stage","edit project stage","manage project task stage","edit project task stage","manage project expense","edit project expense","manage activity","edit activity","manage bug status","edit bug status"],"pos":["create warehouse","create purchase","create quotation","create pos","create barcode","create product","create product category","create product unit","view pos dashboard","view warehouse","view purchase","view quotation","view pos","view product","view product category","view product unit","delete warehouse","delete purchase","delete quotation","delete pos","delete product","delete product category","delete product unit","manage warehouse","edit warehouse","manage purchase","edit purchase","manage quotation","edit quotation","manage pos","edit pos","manage product","edit product","manage product category","edit product category","manage product unit","edit product unit"],"support":["create support","view support dashboard","view support","delete support","manage support","edit support","reply support"],"user_management":["create user","create client","view user","view client","delete user","delete client","manage user","edit user","manage client","edit client"],"booking":["create booking","create appointment","create appointment booking","create calendar event","view booking dashboard","view booking","show booking","view appointment","show appointment","view appointment booking","show appointment booking","view calendar event","show calendar event","delete booking","delete appointment","delete appointment booking","delete calendar event","manage booking","edit booking","manage appointment","edit appointment","manage appointment booking","edit appointment booking","manage calendar event","edit calendar event"],"omx_flow":["access omx flow","whatsapp_flows","whatsapp_orders","campaigns","templates","chatbot"],"personal_tasks":["create personal task","create personal task comment","create personal task file","create personal task checklist","view personal task","delete personal task","delete personal task comment","delete personal task file","delete personal task checklist","manage personal task","edit personal task","edit personal task comment","edit personal task checklist","manage personal task time tracking"],"automatish":["access automatish"]},"storage_limit":0.0,"avatar":"avatar.png","messenger_color":"#2180f3","lang":"en","default_pipeline":null,"active_status":0,"delete_status":1,"mode":"light","dark_mode":0,"is_disable":1,"is_enable_login":1,"is_active":1,"referral_code":0,"used_referral_code":0,"commission_amount":0,"last_login_at":null,"created_by":84,"created_at":"2025-07-25T05:16:21.000000Z","updated_at":"2025-07-25T05:16:21.000000Z","is_email_verified":0,"profile":"http://localhost:8000/storage/avatar.png","pivot":{"lead_id":12,"user_id":88}}],"pipeline":{"id":27,"name":"Default Pipeline","created_by":84,"created_at":"2025-07-21T10:19:26.000000Z","updated_at":"2025-07-21T10:19:26.000000Z"},"old_stage_id":105,"new_stage_id":"106","triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2048 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-25 05:17:41] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2024 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-25T05:17:41.124675Z","source":"crm_webhook_system","action":"crm.lead_stage_changed","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2027.0,"user_id":84,"entity_id":12,"entity_type":"Lead","request_payload":{"action":"crm.lead_stage_changed","timestamp":"2025-07-25T05:17:39.097654Z","data":{"id":12,"name":"Mr Rajkumer Sarkar","email":"<EMAIL>","phone":"+918654589658","subject":"Booking bot","user_id":88,"pipeline_id":27,"stage_id":105,"sources":[],"products":[],"notes":null,"labels":[],"order":0,"created_by":84,"is_active":1,"is_converted":0,"date":"2025-07-25","next_follow_up_date":null,"created_at":"2025-07-25T05:17:15.000000Z","updated_at":"2025-07-25T05:17:15.000000Z","stage":{"id":105,"name":"New","pipeline_id":27,"created_by":84,"order":0,"created_at":"2025-07-21T10:19:26.000000Z","updated_at":"2025-07-21T10:19:26.000000Z"},"users":[{"id":84,"name":"parichay  Bot flow","email":"<EMAIL>","email_verified_at":"2025-07-21T06:08:10.000000Z","plan":16,"plan_expire_date":"2026-07-23","requested_plan":0,"trial_plan":0,"trial_expire_date":null,"type":"company","system_admin_company_id":null,"company_name":null,"company_description":null,"module_permissions":{"crm":["create lead","create deal","create form builder","create contract","create pipeline","create stage","create source","create label","view crm dashboard","view lead","view deal","view form builder","view contract","view pipeline","view stage","view source","view label","delete lead","delete deal","delete form builder","delete contract","delete pipeline","delete stage","delete source","delete label","manage lead","edit lead","manage deal","edit deal","manage form builder","edit form builder","manage contract","edit contract","manage pipeline","edit pipeline","manage stage","edit stage","manage source","edit source","manage label","edit label"],"hrm":["create employee","create set salary","create pay slip","create leave","create attendance","create training","create award","create branch","create department","create designation","create document type","view hrm dashboard","view employee","view set salary","view pay slip","view leave","view attendance","view training","view award","view branch","view department","view designation","view document type","delete employee","delete set salary","delete pay slip","delete leave","delete attendance","delete training","delete award","delete branch","delete department","delete designation","delete document type","manage employee","edit employee","manage set salary","edit set salary","manage pay slip","edit pay slip","manage leave","edit leave","manage attendance","edit attendance","manage training","edit training","manage award","edit award","manage branch","edit branch","manage department","edit department","manage designation","edit designation","manage document type","edit document type"],"account":["create customer","create vender","create invoice","create bill","create revenue","create payment","create proposal","create goal","create credit note","create debit note","create bank account","create bank transfer","create transaction","create chart of account","create journal entry","create assets","create constant custom field","view account dashboard","view customer","view vender","view invoice","view bill","view revenue","view payment","view proposal","view goal","view credit note","view debit note","view bank account","view bank transfer","view transaction","view chart of account","view journal entry","view assets","view constant custom field","view report","delete customer","delete vender","delete invoice","delete bill","delete revenue","delete payment","delete proposal","delete goal","delete credit note","delete debit note","delete bank account","delete bank transfer","delete transaction","delete chart of account","delete journal entry","delete assets","delete constant custom field","manage customer","edit customer","manage vender","edit vender","manage invoice","edit invoice","manage bill","edit bill","manage revenue","edit revenue","manage payment","edit payment","manage proposal","edit proposal","manage goal","edit goal","manage credit note","edit credit note","manage debit note","edit debit note","manage bank account","edit bank account","manage bank transfer","edit bank transfer","manage transaction","edit transaction","manage chart of account","edit chart of account","manage journal entry","edit journal entry","manage assets","edit assets","manage constant custom field","edit constant custom field","manage report"],"project":["create project","create project task","create timesheet","create bug report","create milestone","create project stage","create project task stage","create project expense","create activity","create bug status","view project dashboard","view project","view project task","view timesheet","view bug report","view milestone","view project stage","view project task stage","view project expense","view activity","view bug status","delete project","delete project task","delete timesheet","delete bug report","delete milestone","delete project stage","delete project task stage","delete project expense","delete activity","delete bug status","manage project","edit project","manage project task","edit project task","manage timesheet","edit timesheet","manage bug report","edit bug report","manage milestone","edit milestone","manage project stage","edit project stage","manage project task stage","edit project task stage","manage project expense","edit project expense","manage activity","edit activity","manage bug status","edit bug status"],"pos":["create warehouse","create purchase","create quotation","create pos","create barcode","create product","create product category","create product unit","view pos dashboard","view warehouse","view purchase","view quotation","view pos","view product","view product category","view product unit","delete warehouse","delete purchase","delete quotation","delete pos","delete product","delete product category","delete product unit","manage warehouse","edit warehouse","manage purchase","edit purchase","manage quotation","edit quotation","manage pos","edit pos","manage product","edit product","manage product category","edit product category","manage product unit","edit product unit"],"support":["create support","view support dashboard","view support","delete support","manage support","edit support","reply support"],"user_management":["create user","create client","view user","view client","delete user","delete client","manage user","edit user","manage client","edit client"],"booking":["create booking","create appointment","create appointment booking","create calendar event","view booking dashboard","view booking","show booking","view appointment","show appointment","view appointment booking","show appointment booking","view calendar event","show calendar event","delete booking","delete appointment","delete appointment booking","delete calendar event","manage booking","edit booking","manage appointment","edit appointment","manage appointment booking","edit appointment booking","manage calendar event","edit calendar event"],"omx_flow":["access omx flow","whatsapp_flows","whatsapp_orders","campaigns","templates","chatbot"],"personal_tasks":["create personal task","create personal task comment","create personal task file","create personal task checklist","view personal task","delete personal task","delete personal task comment","delete personal task file","delete personal task checklist","manage personal task","edit personal task","edit personal task comment","edit personal task checklist","manage personal task time tracking"],"automatish":["access automatish"]},"storage_limit":0.0,"avatar":"","messenger_color":"#2180f3","lang":"en","default_pipeline":null,"active_status":0,"delete_status":1,"mode":"light","dark_mode":0,"is_disable":1,"is_enable_login":1,"is_active":1,"referral_code":0,"used_referral_code":0,"commission_amount":0,"last_login_at":null,"created_by":7,"created_at":"2025-07-21T06:08:10.000000Z","updated_at":"2025-07-23T13:27:13.000000Z","is_email_verified":0,"profile":"http://localhost:8000/storage/avatar.png","pivot":{"lead_id":12,"user_id":84}},{"id":88,"name":"Raj Sharma","email":"<EMAIL>","email_verified_at":"2025-07-25T05:16:21.000000Z","plan":null,"plan_expire_date":null,"requested_plan":0,"trial_plan":0,"trial_expire_date":null,"type":"employee","system_admin_company_id":null,"company_name":null,"company_description":null,"module_permissions":{"crm":["create lead","create deal","create form builder","create contract","create pipeline","create stage","create source","create label","view crm dashboard","view lead","view deal","view form builder","view contract","view pipeline","view stage","view source","view label","delete lead","delete deal","delete form builder","delete contract","delete pipeline","delete stage","delete source","delete label","manage lead","edit lead","manage deal","edit deal","manage form builder","edit form builder","manage contract","edit contract","manage pipeline","edit pipeline","manage stage","edit stage","manage source","edit source","manage label","edit label"],"hrm":["create employee","create set salary","create pay slip","create leave","create attendance","create training","create award","create branch","create department","create designation","create document type","view hrm dashboard","view employee","view set salary","view pay slip","view leave","view attendance","view training","view award","view branch","view department","view designation","view document type","delete employee","delete set salary","delete pay slip","delete leave","delete attendance","delete training","delete award","delete branch","delete department","delete designation","delete document type","manage employee","edit employee","manage set salary","edit set salary","manage pay slip","edit pay slip","manage leave","edit leave","manage attendance","edit attendance","manage training","edit training","manage award","edit award","manage branch","edit branch","manage department","edit department","manage designation","edit designation","manage document type","edit document type"],"account":["create customer","create vender","create invoice","create bill","create revenue","create payment","create proposal","create goal","create credit note","create debit note","create bank account","create bank transfer","create transaction","create chart of account","create journal entry","create assets","create constant custom field","view account dashboard","view customer","view vender","view invoice","view bill","view revenue","view payment","view proposal","view goal","view credit note","view debit note","view bank account","view bank transfer","view transaction","view chart of account","view journal entry","view assets","view constant custom field","view report","delete customer","delete vender","delete invoice","delete bill","delete revenue","delete payment","delete proposal","delete goal","delete credit note","delete debit note","delete bank account","delete bank transfer","delete transaction","delete chart of account","delete journal entry","delete assets","delete constant custom field","manage customer","edit customer","manage vender","edit vender","manage invoice","edit invoice","manage bill","edit bill","manage revenue","edit revenue","manage payment","edit payment","manage proposal","edit proposal","manage goal","edit goal","manage credit note","edit credit note","manage debit note","edit debit note","manage bank account","edit bank account","manage bank transfer","edit bank transfer","manage transaction","edit transaction","manage chart of account","edit chart of account","manage journal entry","edit journal entry","manage assets","edit assets","manage constant custom field","edit constant custom field","manage report"],"project":["create project","create project task","create timesheet","create bug report","create milestone","create project stage","create project task stage","create project expense","create activity","create bug status","view project dashboard","view project","view project task","view timesheet","view bug report","view milestone","view project stage","view project task stage","view project expense","view activity","view bug status","delete project","delete project task","delete timesheet","delete bug report","delete milestone","delete project stage","delete project task stage","delete project expense","delete activity","delete bug status","manage project","edit project","manage project task","edit project task","manage timesheet","edit timesheet","manage bug report","edit bug report","manage milestone","edit milestone","manage project stage","edit project stage","manage project task stage","edit project task stage","manage project expense","edit project expense","manage activity","edit activity","manage bug status","edit bug status"],"pos":["create warehouse","create purchase","create quotation","create pos","create barcode","create product","create product category","create product unit","view pos dashboard","view warehouse","view purchase","view quotation","view pos","view product","view product category","view product unit","delete warehouse","delete purchase","delete quotation","delete pos","delete product","delete product category","delete product unit","manage warehouse","edit warehouse","manage purchase","edit purchase","manage quotation","edit quotation","manage pos","edit pos","manage product","edit product","manage product category","edit product category","manage product unit","edit product unit"],"support":["create support","view support dashboard","view support","delete support","manage support","edit support","reply support"],"user_management":["create user","create client","view user","view client","delete user","delete client","manage user","edit user","manage client","edit client"],"booking":["create booking","create appointment","create appointment booking","create calendar event","view booking dashboard","view booking","show booking","view appointment","show appointment","view appointment booking","show appointment booking","view calendar event","show calendar event","delete booking","delete appointment","delete appointment booking","delete calendar event","manage booking","edit booking","manage appointment","edit appointment","manage appointment booking","edit appointment booking","manage calendar event","edit calendar event"],"omx_flow":["access omx flow","whatsapp_flows","whatsapp_orders","campaigns","templates","chatbot"],"personal_tasks":["create personal task","create personal task comment","create personal task file","create personal task checklist","view personal task","delete personal task","delete personal task comment","delete personal task file","delete personal task checklist","manage personal task","edit personal task","edit personal task comment","edit personal task checklist","manage personal task time tracking"],"automatish":["access automatish"]},"storage_limit":0.0,"avatar":"avatar.png","messenger_color":"#2180f3","lang":"en","default_pipeline":null,"active_status":0,"delete_status":1,"mode":"light","dark_mode":0,"is_disable":1,"is_enable_login":1,"is_active":1,"referral_code":0,"used_referral_code":0,"commission_amount":0,"last_login_at":null,"created_by":84,"created_at":"2025-07-25T05:16:21.000000Z","updated_at":"2025-07-25T05:16:21.000000Z","is_email_verified":0,"profile":"http://localhost:8000/storage/avatar.png","pivot":{"lead_id":12,"user_id":88}}],"pipeline":{"id":27,"name":"Default Pipeline","created_by":84,"created_at":"2025-07-21T10:19:26.000000Z","updated_at":"2025-07-21T10:19:26.000000Z"},"old_stage_id":105,"new_stage_id":"106","triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2024 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-25 05:17:41] local.WARNING: Webhook dispatch completed for action: crm.lead_stage_changed. Success: 0, Failed: 2 {"timestamp":"2025-07-25T05:17:41.126717Z","source":"crm_webhook_system","action":"crm.lead_stage_changed","user_id":84,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2048 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2024 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
[2025-07-25 05:17:44] local.INFO: Starting webhook dispatch for action: crm.lead_stage_changed {"timestamp":"2025-07-25T05:17:44.471713Z","source":"crm_webhook_system","action":"crm.lead_stage_changed","user_id":84,"entity_type":"Lead","entity_id":12,"status":"dispatching"} 
[2025-07-25 05:17:46] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2010 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-25T05:17:46.529186Z","source":"crm_webhook_system","action":"crm.lead_stage_changed","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2047.0,"user_id":84,"entity_id":12,"entity_type":"Lead","request_payload":{"action":"crm.lead_stage_changed","timestamp":"2025-07-25T05:17:44.482079Z","data":{"id":12,"name":"Mr Rajkumer Sarkar","email":"<EMAIL>","phone":"+918654589658","subject":"Booking bot","user_id":88,"pipeline_id":27,"stage_id":106,"sources":[],"products":[],"notes":null,"labels":[],"order":0,"created_by":84,"is_active":1,"is_converted":0,"date":"2025-07-25","next_follow_up_date":null,"created_at":"2025-07-25T05:17:15.000000Z","updated_at":"2025-07-25T05:17:41.000000Z","stage":{"id":106,"name":"Qualified","pipeline_id":27,"created_by":84,"order":1,"created_at":"2025-07-21T10:19:26.000000Z","updated_at":"2025-07-21T10:19:26.000000Z"},"users":[{"id":84,"name":"parichay  Bot flow","email":"<EMAIL>","email_verified_at":"2025-07-21T06:08:10.000000Z","plan":16,"plan_expire_date":"2026-07-23","requested_plan":0,"trial_plan":0,"trial_expire_date":null,"type":"company","system_admin_company_id":null,"company_name":null,"company_description":null,"module_permissions":{"crm":["create lead","create deal","create form builder","create contract","create pipeline","create stage","create source","create label","view crm dashboard","view lead","view deal","view form builder","view contract","view pipeline","view stage","view source","view label","delete lead","delete deal","delete form builder","delete contract","delete pipeline","delete stage","delete source","delete label","manage lead","edit lead","manage deal","edit deal","manage form builder","edit form builder","manage contract","edit contract","manage pipeline","edit pipeline","manage stage","edit stage","manage source","edit source","manage label","edit label"],"hrm":["create employee","create set salary","create pay slip","create leave","create attendance","create training","create award","create branch","create department","create designation","create document type","view hrm dashboard","view employee","view set salary","view pay slip","view leave","view attendance","view training","view award","view branch","view department","view designation","view document type","delete employee","delete set salary","delete pay slip","delete leave","delete attendance","delete training","delete award","delete branch","delete department","delete designation","delete document type","manage employee","edit employee","manage set salary","edit set salary","manage pay slip","edit pay slip","manage leave","edit leave","manage attendance","edit attendance","manage training","edit training","manage award","edit award","manage branch","edit branch","manage department","edit department","manage designation","edit designation","manage document type","edit document type"],"account":["create customer","create vender","create invoice","create bill","create revenue","create payment","create proposal","create goal","create credit note","create debit note","create bank account","create bank transfer","create transaction","create chart of account","create journal entry","create assets","create constant custom field","view account dashboard","view customer","view vender","view invoice","view bill","view revenue","view payment","view proposal","view goal","view credit note","view debit note","view bank account","view bank transfer","view transaction","view chart of account","view journal entry","view assets","view constant custom field","view report","delete customer","delete vender","delete invoice","delete bill","delete revenue","delete payment","delete proposal","delete goal","delete credit note","delete debit note","delete bank account","delete bank transfer","delete transaction","delete chart of account","delete journal entry","delete assets","delete constant custom field","manage customer","edit customer","manage vender","edit vender","manage invoice","edit invoice","manage bill","edit bill","manage revenue","edit revenue","manage payment","edit payment","manage proposal","edit proposal","manage goal","edit goal","manage credit note","edit credit note","manage debit note","edit debit note","manage bank account","edit bank account","manage bank transfer","edit bank transfer","manage transaction","edit transaction","manage chart of account","edit chart of account","manage journal entry","edit journal entry","manage assets","edit assets","manage constant custom field","edit constant custom field","manage report"],"project":["create project","create project task","create timesheet","create bug report","create milestone","create project stage","create project task stage","create project expense","create activity","create bug status","view project dashboard","view project","view project task","view timesheet","view bug report","view milestone","view project stage","view project task stage","view project expense","view activity","view bug status","delete project","delete project task","delete timesheet","delete bug report","delete milestone","delete project stage","delete project task stage","delete project expense","delete activity","delete bug status","manage project","edit project","manage project task","edit project task","manage timesheet","edit timesheet","manage bug report","edit bug report","manage milestone","edit milestone","manage project stage","edit project stage","manage project task stage","edit project task stage","manage project expense","edit project expense","manage activity","edit activity","manage bug status","edit bug status"],"pos":["create warehouse","create purchase","create quotation","create pos","create barcode","create product","create product category","create product unit","view pos dashboard","view warehouse","view purchase","view quotation","view pos","view product","view product category","view product unit","delete warehouse","delete purchase","delete quotation","delete pos","delete product","delete product category","delete product unit","manage warehouse","edit warehouse","manage purchase","edit purchase","manage quotation","edit quotation","manage pos","edit pos","manage product","edit product","manage product category","edit product category","manage product unit","edit product unit"],"support":["create support","view support dashboard","view support","delete support","manage support","edit support","reply support"],"user_management":["create user","create client","view user","view client","delete user","delete client","manage user","edit user","manage client","edit client"],"booking":["create booking","create appointment","create appointment booking","create calendar event","view booking dashboard","view booking","show booking","view appointment","show appointment","view appointment booking","show appointment booking","view calendar event","show calendar event","delete booking","delete appointment","delete appointment booking","delete calendar event","manage booking","edit booking","manage appointment","edit appointment","manage appointment booking","edit appointment booking","manage calendar event","edit calendar event"],"omx_flow":["access omx flow","whatsapp_flows","whatsapp_orders","campaigns","templates","chatbot"],"personal_tasks":["create personal task","create personal task comment","create personal task file","create personal task checklist","view personal task","delete personal task","delete personal task comment","delete personal task file","delete personal task checklist","manage personal task","edit personal task","edit personal task comment","edit personal task checklist","manage personal task time tracking"],"automatish":["access automatish"]},"storage_limit":0.0,"avatar":"","messenger_color":"#2180f3","lang":"en","default_pipeline":null,"active_status":0,"delete_status":1,"mode":"light","dark_mode":0,"is_disable":1,"is_enable_login":1,"is_active":1,"referral_code":0,"used_referral_code":0,"commission_amount":0,"last_login_at":null,"created_by":7,"created_at":"2025-07-21T06:08:10.000000Z","updated_at":"2025-07-23T13:27:13.000000Z","is_email_verified":0,"profile":"http://localhost:8000/storage/avatar.png","pivot":{"lead_id":12,"user_id":84}},{"id":88,"name":"Raj Sharma","email":"<EMAIL>","email_verified_at":"2025-07-25T05:16:21.000000Z","plan":null,"plan_expire_date":null,"requested_plan":0,"trial_plan":0,"trial_expire_date":null,"type":"employee","system_admin_company_id":null,"company_name":null,"company_description":null,"module_permissions":{"crm":["create lead","create deal","create form builder","create contract","create pipeline","create stage","create source","create label","view crm dashboard","view lead","view deal","view form builder","view contract","view pipeline","view stage","view source","view label","delete lead","delete deal","delete form builder","delete contract","delete pipeline","delete stage","delete source","delete label","manage lead","edit lead","manage deal","edit deal","manage form builder","edit form builder","manage contract","edit contract","manage pipeline","edit pipeline","manage stage","edit stage","manage source","edit source","manage label","edit label"],"hrm":["create employee","create set salary","create pay slip","create leave","create attendance","create training","create award","create branch","create department","create designation","create document type","view hrm dashboard","view employee","view set salary","view pay slip","view leave","view attendance","view training","view award","view branch","view department","view designation","view document type","delete employee","delete set salary","delete pay slip","delete leave","delete attendance","delete training","delete award","delete branch","delete department","delete designation","delete document type","manage employee","edit employee","manage set salary","edit set salary","manage pay slip","edit pay slip","manage leave","edit leave","manage attendance","edit attendance","manage training","edit training","manage award","edit award","manage branch","edit branch","manage department","edit department","manage designation","edit designation","manage document type","edit document type"],"account":["create customer","create vender","create invoice","create bill","create revenue","create payment","create proposal","create goal","create credit note","create debit note","create bank account","create bank transfer","create transaction","create chart of account","create journal entry","create assets","create constant custom field","view account dashboard","view customer","view vender","view invoice","view bill","view revenue","view payment","view proposal","view goal","view credit note","view debit note","view bank account","view bank transfer","view transaction","view chart of account","view journal entry","view assets","view constant custom field","view report","delete customer","delete vender","delete invoice","delete bill","delete revenue","delete payment","delete proposal","delete goal","delete credit note","delete debit note","delete bank account","delete bank transfer","delete transaction","delete chart of account","delete journal entry","delete assets","delete constant custom field","manage customer","edit customer","manage vender","edit vender","manage invoice","edit invoice","manage bill","edit bill","manage revenue","edit revenue","manage payment","edit payment","manage proposal","edit proposal","manage goal","edit goal","manage credit note","edit credit note","manage debit note","edit debit note","manage bank account","edit bank account","manage bank transfer","edit bank transfer","manage transaction","edit transaction","manage chart of account","edit chart of account","manage journal entry","edit journal entry","manage assets","edit assets","manage constant custom field","edit constant custom field","manage report"],"project":["create project","create project task","create timesheet","create bug report","create milestone","create project stage","create project task stage","create project expense","create activity","create bug status","view project dashboard","view project","view project task","view timesheet","view bug report","view milestone","view project stage","view project task stage","view project expense","view activity","view bug status","delete project","delete project task","delete timesheet","delete bug report","delete milestone","delete project stage","delete project task stage","delete project expense","delete activity","delete bug status","manage project","edit project","manage project task","edit project task","manage timesheet","edit timesheet","manage bug report","edit bug report","manage milestone","edit milestone","manage project stage","edit project stage","manage project task stage","edit project task stage","manage project expense","edit project expense","manage activity","edit activity","manage bug status","edit bug status"],"pos":["create warehouse","create purchase","create quotation","create pos","create barcode","create product","create product category","create product unit","view pos dashboard","view warehouse","view purchase","view quotation","view pos","view product","view product category","view product unit","delete warehouse","delete purchase","delete quotation","delete pos","delete product","delete product category","delete product unit","manage warehouse","edit warehouse","manage purchase","edit purchase","manage quotation","edit quotation","manage pos","edit pos","manage product","edit product","manage product category","edit product category","manage product unit","edit product unit"],"support":["create support","view support dashboard","view support","delete support","manage support","edit support","reply support"],"user_management":["create user","create client","view user","view client","delete user","delete client","manage user","edit user","manage client","edit client"],"booking":["create booking","create appointment","create appointment booking","create calendar event","view booking dashboard","view booking","show booking","view appointment","show appointment","view appointment booking","show appointment booking","view calendar event","show calendar event","delete booking","delete appointment","delete appointment booking","delete calendar event","manage booking","edit booking","manage appointment","edit appointment","manage appointment booking","edit appointment booking","manage calendar event","edit calendar event"],"omx_flow":["access omx flow","whatsapp_flows","whatsapp_orders","campaigns","templates","chatbot"],"personal_tasks":["create personal task","create personal task comment","create personal task file","create personal task checklist","view personal task","delete personal task","delete personal task comment","delete personal task file","delete personal task checklist","manage personal task","edit personal task","edit personal task comment","edit personal task checklist","manage personal task time tracking"],"automatish":["access automatish"]},"storage_limit":0.0,"avatar":"avatar.png","messenger_color":"#2180f3","lang":"en","default_pipeline":null,"active_status":0,"delete_status":1,"mode":"light","dark_mode":0,"is_disable":1,"is_enable_login":1,"is_active":1,"referral_code":0,"used_referral_code":0,"commission_amount":0,"last_login_at":null,"created_by":84,"created_at":"2025-07-25T05:16:21.000000Z","updated_at":"2025-07-25T05:16:21.000000Z","is_email_verified":0,"profile":"http://localhost:8000/storage/avatar.png","pivot":{"lead_id":12,"user_id":88}}],"pipeline":{"id":27,"name":"Default Pipeline","created_by":84,"created_at":"2025-07-21T10:19:26.000000Z","updated_at":"2025-07-21T10:19:26.000000Z"},"old_stage_id":106,"new_stage_id":"105","triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2010 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-25 05:17:48] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2023 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-25T05:17:48.555917Z","source":"crm_webhook_system","action":"crm.lead_stage_changed","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2025.0,"user_id":84,"entity_id":12,"entity_type":"Lead","request_payload":{"action":"crm.lead_stage_changed","timestamp":"2025-07-25T05:17:46.530587Z","data":{"id":12,"name":"Mr Rajkumer Sarkar","email":"<EMAIL>","phone":"+918654589658","subject":"Booking bot","user_id":88,"pipeline_id":27,"stage_id":106,"sources":[],"products":[],"notes":null,"labels":[],"order":0,"created_by":84,"is_active":1,"is_converted":0,"date":"2025-07-25","next_follow_up_date":null,"created_at":"2025-07-25T05:17:15.000000Z","updated_at":"2025-07-25T05:17:41.000000Z","stage":{"id":106,"name":"Qualified","pipeline_id":27,"created_by":84,"order":1,"created_at":"2025-07-21T10:19:26.000000Z","updated_at":"2025-07-21T10:19:26.000000Z"},"users":[{"id":84,"name":"parichay  Bot flow","email":"<EMAIL>","email_verified_at":"2025-07-21T06:08:10.000000Z","plan":16,"plan_expire_date":"2026-07-23","requested_plan":0,"trial_plan":0,"trial_expire_date":null,"type":"company","system_admin_company_id":null,"company_name":null,"company_description":null,"module_permissions":{"crm":["create lead","create deal","create form builder","create contract","create pipeline","create stage","create source","create label","view crm dashboard","view lead","view deal","view form builder","view contract","view pipeline","view stage","view source","view label","delete lead","delete deal","delete form builder","delete contract","delete pipeline","delete stage","delete source","delete label","manage lead","edit lead","manage deal","edit deal","manage form builder","edit form builder","manage contract","edit contract","manage pipeline","edit pipeline","manage stage","edit stage","manage source","edit source","manage label","edit label"],"hrm":["create employee","create set salary","create pay slip","create leave","create attendance","create training","create award","create branch","create department","create designation","create document type","view hrm dashboard","view employee","view set salary","view pay slip","view leave","view attendance","view training","view award","view branch","view department","view designation","view document type","delete employee","delete set salary","delete pay slip","delete leave","delete attendance","delete training","delete award","delete branch","delete department","delete designation","delete document type","manage employee","edit employee","manage set salary","edit set salary","manage pay slip","edit pay slip","manage leave","edit leave","manage attendance","edit attendance","manage training","edit training","manage award","edit award","manage branch","edit branch","manage department","edit department","manage designation","edit designation","manage document type","edit document type"],"account":["create customer","create vender","create invoice","create bill","create revenue","create payment","create proposal","create goal","create credit note","create debit note","create bank account","create bank transfer","create transaction","create chart of account","create journal entry","create assets","create constant custom field","view account dashboard","view customer","view vender","view invoice","view bill","view revenue","view payment","view proposal","view goal","view credit note","view debit note","view bank account","view bank transfer","view transaction","view chart of account","view journal entry","view assets","view constant custom field","view report","delete customer","delete vender","delete invoice","delete bill","delete revenue","delete payment","delete proposal","delete goal","delete credit note","delete debit note","delete bank account","delete bank transfer","delete transaction","delete chart of account","delete journal entry","delete assets","delete constant custom field","manage customer","edit customer","manage vender","edit vender","manage invoice","edit invoice","manage bill","edit bill","manage revenue","edit revenue","manage payment","edit payment","manage proposal","edit proposal","manage goal","edit goal","manage credit note","edit credit note","manage debit note","edit debit note","manage bank account","edit bank account","manage bank transfer","edit bank transfer","manage transaction","edit transaction","manage chart of account","edit chart of account","manage journal entry","edit journal entry","manage assets","edit assets","manage constant custom field","edit constant custom field","manage report"],"project":["create project","create project task","create timesheet","create bug report","create milestone","create project stage","create project task stage","create project expense","create activity","create bug status","view project dashboard","view project","view project task","view timesheet","view bug report","view milestone","view project stage","view project task stage","view project expense","view activity","view bug status","delete project","delete project task","delete timesheet","delete bug report","delete milestone","delete project stage","delete project task stage","delete project expense","delete activity","delete bug status","manage project","edit project","manage project task","edit project task","manage timesheet","edit timesheet","manage bug report","edit bug report","manage milestone","edit milestone","manage project stage","edit project stage","manage project task stage","edit project task stage","manage project expense","edit project expense","manage activity","edit activity","manage bug status","edit bug status"],"pos":["create warehouse","create purchase","create quotation","create pos","create barcode","create product","create product category","create product unit","view pos dashboard","view warehouse","view purchase","view quotation","view pos","view product","view product category","view product unit","delete warehouse","delete purchase","delete quotation","delete pos","delete product","delete product category","delete product unit","manage warehouse","edit warehouse","manage purchase","edit purchase","manage quotation","edit quotation","manage pos","edit pos","manage product","edit product","manage product category","edit product category","manage product unit","edit product unit"],"support":["create support","view support dashboard","view support","delete support","manage support","edit support","reply support"],"user_management":["create user","create client","view user","view client","delete user","delete client","manage user","edit user","manage client","edit client"],"booking":["create booking","create appointment","create appointment booking","create calendar event","view booking dashboard","view booking","show booking","view appointment","show appointment","view appointment booking","show appointment booking","view calendar event","show calendar event","delete booking","delete appointment","delete appointment booking","delete calendar event","manage booking","edit booking","manage appointment","edit appointment","manage appointment booking","edit appointment booking","manage calendar event","edit calendar event"],"omx_flow":["access omx flow","whatsapp_flows","whatsapp_orders","campaigns","templates","chatbot"],"personal_tasks":["create personal task","create personal task comment","create personal task file","create personal task checklist","view personal task","delete personal task","delete personal task comment","delete personal task file","delete personal task checklist","manage personal task","edit personal task","edit personal task comment","edit personal task checklist","manage personal task time tracking"],"automatish":["access automatish"]},"storage_limit":0.0,"avatar":"","messenger_color":"#2180f3","lang":"en","default_pipeline":null,"active_status":0,"delete_status":1,"mode":"light","dark_mode":0,"is_disable":1,"is_enable_login":1,"is_active":1,"referral_code":0,"used_referral_code":0,"commission_amount":0,"last_login_at":null,"created_by":7,"created_at":"2025-07-21T06:08:10.000000Z","updated_at":"2025-07-23T13:27:13.000000Z","is_email_verified":0,"profile":"http://localhost:8000/storage/avatar.png","pivot":{"lead_id":12,"user_id":84}},{"id":88,"name":"Raj Sharma","email":"<EMAIL>","email_verified_at":"2025-07-25T05:16:21.000000Z","plan":null,"plan_expire_date":null,"requested_plan":0,"trial_plan":0,"trial_expire_date":null,"type":"employee","system_admin_company_id":null,"company_name":null,"company_description":null,"module_permissions":{"crm":["create lead","create deal","create form builder","create contract","create pipeline","create stage","create source","create label","view crm dashboard","view lead","view deal","view form builder","view contract","view pipeline","view stage","view source","view label","delete lead","delete deal","delete form builder","delete contract","delete pipeline","delete stage","delete source","delete label","manage lead","edit lead","manage deal","edit deal","manage form builder","edit form builder","manage contract","edit contract","manage pipeline","edit pipeline","manage stage","edit stage","manage source","edit source","manage label","edit label"],"hrm":["create employee","create set salary","create pay slip","create leave","create attendance","create training","create award","create branch","create department","create designation","create document type","view hrm dashboard","view employee","view set salary","view pay slip","view leave","view attendance","view training","view award","view branch","view department","view designation","view document type","delete employee","delete set salary","delete pay slip","delete leave","delete attendance","delete training","delete award","delete branch","delete department","delete designation","delete document type","manage employee","edit employee","manage set salary","edit set salary","manage pay slip","edit pay slip","manage leave","edit leave","manage attendance","edit attendance","manage training","edit training","manage award","edit award","manage branch","edit branch","manage department","edit department","manage designation","edit designation","manage document type","edit document type"],"account":["create customer","create vender","create invoice","create bill","create revenue","create payment","create proposal","create goal","create credit note","create debit note","create bank account","create bank transfer","create transaction","create chart of account","create journal entry","create assets","create constant custom field","view account dashboard","view customer","view vender","view invoice","view bill","view revenue","view payment","view proposal","view goal","view credit note","view debit note","view bank account","view bank transfer","view transaction","view chart of account","view journal entry","view assets","view constant custom field","view report","delete customer","delete vender","delete invoice","delete bill","delete revenue","delete payment","delete proposal","delete goal","delete credit note","delete debit note","delete bank account","delete bank transfer","delete transaction","delete chart of account","delete journal entry","delete assets","delete constant custom field","manage customer","edit customer","manage vender","edit vender","manage invoice","edit invoice","manage bill","edit bill","manage revenue","edit revenue","manage payment","edit payment","manage proposal","edit proposal","manage goal","edit goal","manage credit note","edit credit note","manage debit note","edit debit note","manage bank account","edit bank account","manage bank transfer","edit bank transfer","manage transaction","edit transaction","manage chart of account","edit chart of account","manage journal entry","edit journal entry","manage assets","edit assets","manage constant custom field","edit constant custom field","manage report"],"project":["create project","create project task","create timesheet","create bug report","create milestone","create project stage","create project task stage","create project expense","create activity","create bug status","view project dashboard","view project","view project task","view timesheet","view bug report","view milestone","view project stage","view project task stage","view project expense","view activity","view bug status","delete project","delete project task","delete timesheet","delete bug report","delete milestone","delete project stage","delete project task stage","delete project expense","delete activity","delete bug status","manage project","edit project","manage project task","edit project task","manage timesheet","edit timesheet","manage bug report","edit bug report","manage milestone","edit milestone","manage project stage","edit project stage","manage project task stage","edit project task stage","manage project expense","edit project expense","manage activity","edit activity","manage bug status","edit bug status"],"pos":["create warehouse","create purchase","create quotation","create pos","create barcode","create product","create product category","create product unit","view pos dashboard","view warehouse","view purchase","view quotation","view pos","view product","view product category","view product unit","delete warehouse","delete purchase","delete quotation","delete pos","delete product","delete product category","delete product unit","manage warehouse","edit warehouse","manage purchase","edit purchase","manage quotation","edit quotation","manage pos","edit pos","manage product","edit product","manage product category","edit product category","manage product unit","edit product unit"],"support":["create support","view support dashboard","view support","delete support","manage support","edit support","reply support"],"user_management":["create user","create client","view user","view client","delete user","delete client","manage user","edit user","manage client","edit client"],"booking":["create booking","create appointment","create appointment booking","create calendar event","view booking dashboard","view booking","show booking","view appointment","show appointment","view appointment booking","show appointment booking","view calendar event","show calendar event","delete booking","delete appointment","delete appointment booking","delete calendar event","manage booking","edit booking","manage appointment","edit appointment","manage appointment booking","edit appointment booking","manage calendar event","edit calendar event"],"omx_flow":["access omx flow","whatsapp_flows","whatsapp_orders","campaigns","templates","chatbot"],"personal_tasks":["create personal task","create personal task comment","create personal task file","create personal task checklist","view personal task","delete personal task","delete personal task comment","delete personal task file","delete personal task checklist","manage personal task","edit personal task","edit personal task comment","edit personal task checklist","manage personal task time tracking"],"automatish":["access automatish"]},"storage_limit":0.0,"avatar":"avatar.png","messenger_color":"#2180f3","lang":"en","default_pipeline":null,"active_status":0,"delete_status":1,"mode":"light","dark_mode":0,"is_disable":1,"is_enable_login":1,"is_active":1,"referral_code":0,"used_referral_code":0,"commission_amount":0,"last_login_at":null,"created_by":84,"created_at":"2025-07-25T05:16:21.000000Z","updated_at":"2025-07-25T05:16:21.000000Z","is_email_verified":0,"profile":"http://localhost:8000/storage/avatar.png","pivot":{"lead_id":12,"user_id":88}}],"pipeline":{"id":27,"name":"Default Pipeline","created_by":84,"created_at":"2025-07-21T10:19:26.000000Z","updated_at":"2025-07-21T10:19:26.000000Z"},"old_stage_id":106,"new_stage_id":"105","triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2023 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-25 05:17:48] local.WARNING: Webhook dispatch completed for action: crm.lead_stage_changed. Success: 0, Failed: 2 {"timestamp":"2025-07-25T05:17:48.557785Z","source":"crm_webhook_system","action":"crm.lead_stage_changed","user_id":84,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2010 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2023 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
[2025-07-25 06:19:42] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-25T06:19:42.407922Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"entity_type":"Booking","entity_id":34,"status":"dispatching"} 
[2025-07-25 06:19:46] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2028 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-25T06:19:46.011576Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":3566.0,"user_id":84,"entity_id":34,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-25T06:19:42.445056Z","data":{"event_id":5,"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-31","time":"14:30","selected_location":{"type":"meet","value":"https://meet.google.com/xkd-csbv-uyp","display":"Google Meet"},"custom_fields":["business_type"],"custom_fields_value":["sit"],"updated_at":"2025-07-25T06:19:42.000000Z","created_at":"2025-07-25T06:19:42.000000Z","id":34,"form_data":{"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-31","time":"14:30","selected_location":{"type":"meet","value":"https://meet.google.com/xkd-csbv-uyp","display":"Google Meet"},"custom_fields":["business_type"],"custom_fields_value":["sit"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2028 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-25 06:19:48] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2034 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-25T06:19:48.048304Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2035.0,"user_id":84,"entity_id":34,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-25T06:19:46.013017Z","data":{"event_id":5,"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-31","time":"14:30","selected_location":{"type":"meet","value":"https://meet.google.com/xkd-csbv-uyp","display":"Google Meet"},"custom_fields":["business_type"],"custom_fields_value":["sit"],"updated_at":"2025-07-25T06:19:42.000000Z","created_at":"2025-07-25T06:19:42.000000Z","id":34,"form_data":{"name":"Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-31","time":"14:30","selected_location":{"type":"meet","value":"https://meet.google.com/xkd-csbv-uyp","display":"Google Meet"},"custom_fields":["business_type"],"custom_fields_value":["sit"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2034 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-25 06:19:48] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 2 {"timestamp":"2025-07-25T06:19:48.049214Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2028 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2034 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
[2025-07-25 06:20:48] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-25T06:20:48.402298Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"entity_type":"Booking","entity_id":35,"status":"dispatching"} 
[2025-07-25 06:20:50] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2055 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-25T06:20:50.499607Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2088.0,"user_id":84,"entity_id":35,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-25T06:20:48.411194Z","data":{"event_id":5,"name":"Parichay Singha Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-30","time":"13:30","selected_location":{"type":"zoom","value":"https://app.zoom.us/wc/***********","display":"Zoom"},"custom_fields":["business_type"],"custom_fields_value":["Smart Internz"],"updated_at":"2025-07-25T06:20:48.000000Z","created_at":"2025-07-25T06:20:48.000000Z","id":35,"form_data":{"name":"Parichay Singha Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-30","time":"13:30","selected_location":{"type":"zoom","value":"https://app.zoom.us/wc/***********","display":"Zoom"},"custom_fields":["business_type"],"custom_fields_value":["Smart Internz"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2055 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-25 06:20:52] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2028 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-25T06:20:52.529972Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2030.0,"user_id":84,"entity_id":35,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-25T06:20:50.500265Z","data":{"event_id":5,"name":"Parichay Singha Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-30","time":"13:30","selected_location":{"type":"zoom","value":"https://app.zoom.us/wc/***********","display":"Zoom"},"custom_fields":["business_type"],"custom_fields_value":["Smart Internz"],"updated_at":"2025-07-25T06:20:48.000000Z","created_at":"2025-07-25T06:20:48.000000Z","id":35,"form_data":{"name":"Parichay Singha Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-30","time":"13:30","selected_location":{"type":"zoom","value":"https://app.zoom.us/wc/***********","display":"Zoom"},"custom_fields":["business_type"],"custom_fields_value":["Smart Internz"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2028 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-25 06:20:52] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 2 {"timestamp":"2025-07-25T06:20:52.530706Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2055 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2028 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
[2025-07-25 06:27:26] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-25T06:27:26.613406Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"entity_type":"Booking","entity_id":36,"status":"dispatching"} 
[2025-07-25 06:27:28] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2023 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-25T06:27:28.680041Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2059.0,"user_id":84,"entity_id":36,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-25T06:27:26.621037Z","data":{"event_id":5,"name":"Parichay Singha Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-29","time":"15:00","selected_location":{"type":"meet","value":"https://meet.google.com/xkd-csbv-uyp","display":"Google Meet"},"custom_fields":["business_type"],"custom_fields_value":["Smart Internz"],"updated_at":"2025-07-25T06:27:26.000000Z","created_at":"2025-07-25T06:27:26.000000Z","id":36,"form_data":{"name":"Parichay Singha Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-29","time":"15:00","selected_location":{"type":"meet","value":"https://meet.google.com/xkd-csbv-uyp","display":"Google Meet"},"custom_fields":["business_type"],"custom_fields_value":["Smart Internz"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2023 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-25 06:27:30] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2017 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-25T06:27:30.699024Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2019.0,"user_id":84,"entity_id":36,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-25T06:27:28.680469Z","data":{"event_id":5,"name":"Parichay Singha Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-29","time":"15:00","selected_location":{"type":"meet","value":"https://meet.google.com/xkd-csbv-uyp","display":"Google Meet"},"custom_fields":["business_type"],"custom_fields_value":["Smart Internz"],"updated_at":"2025-07-25T06:27:26.000000Z","created_at":"2025-07-25T06:27:26.000000Z","id":36,"form_data":{"name":"Parichay Singha Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-29","time":"15:00","selected_location":{"type":"meet","value":"https://meet.google.com/xkd-csbv-uyp","display":"Google Meet"},"custom_fields":["business_type"],"custom_fields_value":["Smart Internz"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2017 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-25 06:27:30] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 2 {"timestamp":"2025-07-25T06:27:30.699932Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2023 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2017 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
[2025-07-25 06:46:37] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-25T06:46:37.386645Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"entity_type":"Booking","entity_id":37,"status":"dispatching"} 
[2025-07-25 06:46:39] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2003 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-25T06:46:39.467146Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2065.0,"user_id":84,"entity_id":37,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-25T06:46:37.402631Z","data":{"event_id":5,"name":"Parichay Singha Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-29","time":"15:30","selected_location":{"type":"meet","value":"https://meet.google.com/xkd-csbv-uyp","display":"Google Meet"},"custom_fields":["business_type"],"custom_fields_value":["fzgxcghkl"],"updated_at":"2025-07-25T06:46:37.000000Z","created_at":"2025-07-25T06:46:37.000000Z","id":37,"form_data":{"name":"Parichay Singha Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-29","time":"15:30","selected_location":{"type":"meet","value":"https://meet.google.com/xkd-csbv-uyp","display":"Google Meet"},"custom_fields":["business_type"],"custom_fields_value":["fzgxcghkl"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2003 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-25 06:46:41] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2012 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-25T06:46:41.498343Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2030.0,"user_id":84,"entity_id":37,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-25T06:46:39.468511Z","data":{"event_id":5,"name":"Parichay Singha Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-29","time":"15:30","selected_location":{"type":"meet","value":"https://meet.google.com/xkd-csbv-uyp","display":"Google Meet"},"custom_fields":["business_type"],"custom_fields_value":["fzgxcghkl"],"updated_at":"2025-07-25T06:46:37.000000Z","created_at":"2025-07-25T06:46:37.000000Z","id":37,"form_data":{"name":"Parichay Singha Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-29","time":"15:30","selected_location":{"type":"meet","value":"https://meet.google.com/xkd-csbv-uyp","display":"Google Meet"},"custom_fields":["business_type"],"custom_fields_value":["fzgxcghkl"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2012 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-25 06:46:41] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 2 {"timestamp":"2025-07-25T06:46:41.499658Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2003 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2012 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
[2025-07-25 06:48:25] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-25T06:48:25.508834Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":null,"entity_type":"Booking","entity_id":38,"status":"dispatching"} 
[2025-07-25 06:48:27] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2038 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-25T06:48:27.574668Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2060.0,"user_id":null,"entity_id":38,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-25T06:48:25.514673Z","data":{"event_id":5,"name":"Parichay Singha Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-30","time":"14:30","selected_location":{"type":"phone","value":"8617555736","display":"Phone call"},"custom_fields":["business_type"],"custom_fields_value":["hi this"],"updated_at":"2025-07-25T06:48:25.000000Z","created_at":"2025-07-25T06:48:25.000000Z","id":38,"form_data":{"name":"Parichay Singha Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-30","time":"14:30","selected_location":{"type":"phone","value":"8617555736","display":"Phone call"},"custom_fields":["business_type"],"custom_fields_value":["hi this"],"booking_type":"public"}},"user_id":null,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2038 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-25 06:48:29] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2045 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-25T06:48:29.622150Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2047.0,"user_id":null,"entity_id":38,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-25T06:48:27.575310Z","data":{"event_id":5,"name":"Parichay Singha Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-30","time":"14:30","selected_location":{"type":"phone","value":"8617555736","display":"Phone call"},"custom_fields":["business_type"],"custom_fields_value":["hi this"],"updated_at":"2025-07-25T06:48:25.000000Z","created_at":"2025-07-25T06:48:25.000000Z","id":38,"form_data":{"name":"Parichay Singha Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-30","time":"14:30","selected_location":{"type":"phone","value":"8617555736","display":"Phone call"},"custom_fields":["business_type"],"custom_fields_value":["hi this"],"booking_type":"public"}},"user_id":null,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2045 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-25 06:48:29] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 2 {"timestamp":"2025-07-25T06:48:29.623591Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":null,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2038 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2045 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
[2025-07-25 06:48:45] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-25T06:48:45.605407Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":null,"entity_type":"Booking","entity_id":39,"status":"dispatching"} 
[2025-07-25 06:48:47] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2029 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-25T06:48:47.678949Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2067.0,"user_id":null,"entity_id":39,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-25T06:48:45.612361Z","data":{"event_id":5,"name":"Parichay Singha Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-28","time":"16:30","selected_location":{"type":"phone","value":"8617555736","display":"Phone call"},"custom_fields":["business_type"],"custom_fields_value":["hi this"],"updated_at":"2025-07-25T06:48:45.000000Z","created_at":"2025-07-25T06:48:45.000000Z","id":39,"form_data":{"name":"Parichay Singha Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-28","time":"16:30","selected_location":{"type":"phone","value":"8617555736","display":"Phone call"},"custom_fields":["business_type"],"custom_fields_value":["hi this"],"booking_type":"public"}},"user_id":null,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2029 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-25 06:48:49] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2018 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-25T06:48:49.700988Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2021.0,"user_id":null,"entity_id":39,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-25T06:48:47.680390Z","data":{"event_id":5,"name":"Parichay Singha Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-28","time":"16:30","selected_location":{"type":"phone","value":"8617555736","display":"Phone call"},"custom_fields":["business_type"],"custom_fields_value":["hi this"],"updated_at":"2025-07-25T06:48:45.000000Z","created_at":"2025-07-25T06:48:45.000000Z","id":39,"form_data":{"name":"Parichay Singha Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-28","time":"16:30","selected_location":{"type":"phone","value":"8617555736","display":"Phone call"},"custom_fields":["business_type"],"custom_fields_value":["hi this"],"booking_type":"public"}},"user_id":null,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2018 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-25 06:48:49] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 2 {"timestamp":"2025-07-25T06:48:49.702606Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":null,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2029 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2018 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
[2025-07-25 06:52:41] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-25T06:52:41.307815Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"entity_type":"Booking","entity_id":40,"status":"dispatching"} 
[2025-07-25 06:52:43] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2018 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-25T06:52:43.373547Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2056.0,"user_id":84,"entity_id":40,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-25T06:52:41.317510Z","data":{"event_id":5,"name":"Parichay Singha Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-31","time":"14:30","selected_location":{"type":"skype","value":"https://meet.google.com/xkd-csbv-uyp","display":"Skype"},"custom_fields":["business_type"],"custom_fields_value":["Enter business type"],"updated_at":"2025-07-25T06:52:41.000000Z","created_at":"2025-07-25T06:52:41.000000Z","id":40,"form_data":{"name":"Parichay Singha Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-31","time":"14:30","selected_location":{"type":"skype","value":"https://meet.google.com/xkd-csbv-uyp","display":"Skype"},"custom_fields":["business_type"],"custom_fields_value":["Enter business type"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2018 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-25 06:52:45] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2033 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-25T06:52:45.426582Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2052.0,"user_id":84,"entity_id":40,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-25T06:52:43.374824Z","data":{"event_id":5,"name":"Parichay Singha Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-31","time":"14:30","selected_location":{"type":"skype","value":"https://meet.google.com/xkd-csbv-uyp","display":"Skype"},"custom_fields":["business_type"],"custom_fields_value":["Enter business type"],"updated_at":"2025-07-25T06:52:41.000000Z","created_at":"2025-07-25T06:52:41.000000Z","id":40,"form_data":{"name":"Parichay Singha Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-31","time":"14:30","selected_location":{"type":"skype","value":"https://meet.google.com/xkd-csbv-uyp","display":"Skype"},"custom_fields":["business_type"],"custom_fields_value":["Enter business type"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2033 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-25 06:52:45] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 2 {"timestamp":"2025-07-25T06:52:45.427521Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2018 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2033 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
[2025-07-25 07:04:38] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-25T07:04:38.668468Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"entity_type":"Booking","entity_id":41,"status":"dispatching"} 
[2025-07-25 07:04:40] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2017 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-25T07:04:40.763283Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2084.0,"user_id":84,"entity_id":41,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-25T07:04:38.679463Z","data":{"event_id":5,"name":"Parichay Singha Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-30","time":"12:30","selected_location":{"type":"skype","value":"https://meet.google.com/xkd-csbv-uyp","display":"Skype"},"custom_fields":["business_type"],"custom_fields_value":["Smart Internz"],"updated_at":"2025-07-25T07:04:38.000000Z","created_at":"2025-07-25T07:04:38.000000Z","id":41,"form_data":{"name":"Parichay Singha Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-30","time":"12:30","selected_location":{"type":"skype","value":"https://meet.google.com/xkd-csbv-uyp","display":"Skype"},"custom_fields":["business_type"],"custom_fields_value":["Smart Internz"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2017 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-25 07:04:42] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2027 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-25T07:04:42.794217Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2030.0,"user_id":84,"entity_id":41,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-25T07:04:40.764651Z","data":{"event_id":5,"name":"Parichay Singha Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-30","time":"12:30","selected_location":{"type":"skype","value":"https://meet.google.com/xkd-csbv-uyp","display":"Skype"},"custom_fields":["business_type"],"custom_fields_value":["Smart Internz"],"updated_at":"2025-07-25T07:04:38.000000Z","created_at":"2025-07-25T07:04:38.000000Z","id":41,"form_data":{"name":"Parichay Singha Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-30","time":"12:30","selected_location":{"type":"skype","value":"https://meet.google.com/xkd-csbv-uyp","display":"Skype"},"custom_fields":["business_type"],"custom_fields_value":["Smart Internz"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2027 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-25 07:04:42] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 2 {"timestamp":"2025-07-25T07:04:42.794703Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2017 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2027 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
[2025-07-25 07:11:37] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-25T07:11:37.748874Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"entity_type":"Booking","entity_id":43,"status":"dispatching"} 
[2025-07-25 07:11:39] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2006 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-25T07:11:39.801687Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2045.0,"user_id":84,"entity_id":43,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-25T07:11:37.756758Z","data":{"event_id":5,"name":"Parichay Singha Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-29","time":"14:00","selected_location":{"type":"phone","value":"8617555736","display":"Phone call"},"custom_fields":["business_type"],"custom_fields_value":["afgb"],"updated_at":"2025-07-25T07:11:37.000000Z","created_at":"2025-07-25T07:11:37.000000Z","id":43,"form_data":{"name":"Parichay Singha Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-29","time":"14:00","selected_location":{"type":"phone","value":"8617555736","display":"Phone call"},"custom_fields":["business_type"],"custom_fields_value":["afgb"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2006 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-25 07:11:41] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2019 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-25T07:11:41.824669Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2022.0,"user_id":84,"entity_id":43,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-25T07:11:39.802912Z","data":{"event_id":5,"name":"Parichay Singha Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-29","time":"14:00","selected_location":{"type":"phone","value":"8617555736","display":"Phone call"},"custom_fields":["business_type"],"custom_fields_value":["afgb"],"updated_at":"2025-07-25T07:11:37.000000Z","created_at":"2025-07-25T07:11:37.000000Z","id":43,"form_data":{"name":"Parichay Singha Parichay Singha","email":"<EMAIL>","phone":"***********","date":"2025-07-29","time":"14:00","selected_location":{"type":"phone","value":"8617555736","display":"Phone call"},"custom_fields":["business_type"],"custom_fields_value":["afgb"],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2019 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-25 07:11:41] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 2 {"timestamp":"2025-07-25T07:11:41.826693Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2006 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2019 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
