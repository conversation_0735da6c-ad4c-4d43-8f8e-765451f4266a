@extends('layouts.admin')

@section('page-title')
    {{__('Smart Scheduler')}}
@endsection
@section('action-btn')
<!-- Navigation Buttons -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body p-3">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="d-flex justify-content-center flex-grow-1 align-items-center">
                        <div class="view-switcher-container">
                            <div class="btn-group view-switcher" role="group">
                                <button type="button" onclick="switchView('calendar')" id="calendar-btn" class="btn bg-primary view-btn">
                                    <i class="ti ti-calendar"></i>
                                    <span class="btn-text">{{ __('Calendar') }}</span>
                                </button>
                                <button type="button" onclick="switchView('events')" id="events-btn" class="btn btn-outline-primary view-btn">
                                    <i class="ti ti-calendar-event"></i>
                                    <span class="btn-text">{{ __('Create/Edit') }}</span>
                                </button>
                                <!-- <button type="button" onclick="switchView('appointment')" id="appointment-btn" class="btn btn-outline-primary view-btn">
                                    <i class="ti ti-calendar-plus"></i>
                                    <span class="btn-text">{{ __('Appointment') }}</span>
                                </button> -->
                                <button type="button" onclick="switchView('bookings')" id="bookings-btn" class="btn btn-outline-primary view-btn">
                                    <i class="ti ti-users"></i>
                                    <span class="btn-text">{{ __('Appointments') }}</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection


@push('css-page')
    <link href='https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.css' rel='stylesheet' />
    <script src='https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.js'></script>
    <style>
       /* Button Group Active/Inactive States */
.btn-group.view-switcher .btn {
    transition: all 0.3s ease !important;
    font-weight: 500 !important;
    border: 1px solid !important;
    min-width: 130px !important;
    padding: 10px 18px !important;
    cursor: pointer !important;
}

/* Active State - Filled Primary */
.btn-group.view-switcher .btn.bg-primary,
.btn-group.view-switcher .btn.btn-primary,
.btn-group.view-switcher .btn.active {
    background-color: #6f42c1 !important;
    border-color: #6f42c1 !important;
    color: white !important;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15) !important;
}

/* Inactive State - Outline Primary */
.btn-group.view-switcher .btn.btn-outline-primary {
    background-color: transparent !important;
    border-color: #5e5e5eff !important;
    color: #6f42c1 !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05) !important;
}

/* Hover Effects */
.btn-group.view-switcher .btn.btn-outline-primary:hover {
    background-color: rgba(111, 66, 193, 0.1) !important;
    border-color: #6f42c1 !important;
    color: #6f42c1 !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
}

.btn-group.view-switcher .btn.bg-primary:hover,
.btn-group.view-switcher .btn.btn-primary:hover,
.btn-group.view-switcher .btn.active:hover {
    background-color: #6f42c1 !important;
    border-color: #6f42c1 !important;
    color: white !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2) !important;
}

/* Force button states */
.btn-group.view-switcher .btn:not(.active):not(.bg-primary):not(.btn-primary) {
    background-color: transparent !important;
    color: #6f42c1 !important;
}

/* Bookings Custom Field Styles */
.custom-field-card {
    transition: all 0.3s ease;
    border: 1px solid #e9ecef !important;
}

.custom-field-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1) !important;
}

.custom-field-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bs-primary);
    border-radius: 50%;
    color: white;
}

.field-value {
    word-break: break-word;
    line-height: 1.5;
}

/* Action buttons styling */
.btn-group .btn {
    margin-right: 2px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* Table responsive improvements */
.table-responsive {
    border-radius: 0.375rem;
    overflow: hidden;
}

.table th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
}

.table td {
    vertical-align: middle;
}

/* Modal improvements */
.modal-header.bg-info,
.modal-header.bg-warning {
    border-bottom: none;
}

.modal-footer {
    border-top: 1px solid #dee2e6;
}

/* View Switcher Container */
.view-switcher-container {
    width: 100%;
    display: flex;
    justify-content: center;
}

/* Button Group Base Styles */
.btn-group.view-switcher {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    width: 100%;
    max-width: 600px;
}

.btn-group.view-switcher .btn {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 12px 8px;
    border-radius: 12px !important;
    font-weight: 500;
    transition: all 0.3s ease;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    position: relative;
}

.btn-group.view-switcher .btn i {
    font-size: 18px;
    margin-bottom: 4px;
    display: block;
}

.btn-group.view-switcher .btn .btn-text {
    font-size: 12px;
    line-height: 1.2;
    display: block;
}

/* Desktop Styles */
@media (min-width: 992px) {
    .btn-group.view-switcher {
        flex-wrap: nowrap;
        gap: 12px;
    }

    .btn-group.view-switcher .btn {
        flex-direction: row;
        padding: 12px 20px;
        min-width: 140px;
    }

    .btn-group.view-switcher .btn i {
        font-size: 16px;
        margin-bottom: 0;
        margin-right: 8px;
    }

    .btn-group.view-switcher .btn .btn-text {
        font-size: 14px;
    }
}

/* Tablet Styles */
@media (min-width: 768px) and (max-width: 991.98px) {
    .btn-group.view-switcher {
        flex-wrap: nowrap;
        gap: 10px;
    }

    .btn-group.view-switcher .btn {
        flex-direction: row;
        padding: 10px 16px;
        min-width: 120px;
    }

    .btn-group.view-switcher .btn i {
        font-size: 16px;
        margin-bottom: 0;
        margin-right: 6px;
    }

    .btn-group.view-switcher .btn .btn-text {
        font-size: 13px;
    }
}

/* Mobile Styles */
@media (max-width: 767.98px) {
    .btn-group.view-switcher {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 8px;
        width: 100%;
    }

    .btn-group.view-switcher .btn {
        flex-direction: column;
        padding: 12px 8px;
        min-height: 70px;
        font-size: 11px;
    }

    .btn-group.view-switcher .btn i {
        font-size: 20px;
        margin-bottom: 4px;
    }

    .btn-group.view-switcher .btn .btn-text {
        font-size: 11px;
        font-weight: 600;
    }
}

/* Extra Small Mobile */
@media (max-width: 575.98px) {
    .btn-group.view-switcher {
        grid-template-columns: 1fr;
        gap: 6px;
    }

    .btn-group.view-switcher .btn {
        flex-direction: row;
        padding: 10px 16px;
        min-height: 50px;
        justify-content: flex-start;
    }

    .btn-group.view-switcher .btn i {
        font-size: 18px;
        margin-bottom: 0;
        margin-right: 10px;
    }

    .btn-group.view-switcher .btn .btn-text {
        font-size: 14px;
        font-weight: 500;
    }
}

/* Weekly Availability Styles */
.form-part {
    background: #f8f9fa;
    padding: 12px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    margin-bottom: 12px;
}

.part-title {
    font-size: 1.1em;
    font-weight: 600;
    color: #495057;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid #007bff;
}

.week-availability {
    background: white;
    padding: 12px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.day-card {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    min-height: 120px;
}

.day-card:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0,123,255,0.15);
}

.day-card.active {
    border-color: #007bff;
    background-color: #f8f9ff;
}

.day-header {
    cursor: pointer;
    padding: 5px;
}

.day-header h6 {
    color: #495057;
    font-weight: 600;
}

.day-slots {
    margin-top: 10px;
    text-align: left;
}

.time-slot {
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 4px;
    padding: 8px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.time-slot input {
    border: none;
    background: transparent;
    font-size: 12px;
    width: 70px;
}

.time-slot .remove-slot {
    color: #dc3545;
    cursor: pointer;
    font-size: 14px;
}

.time-slot .remove-slot:hover {
    color: #c82333;
}
.week-availability-vertical {
    background: white;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.day-availability {
    margin-bottom: 15px;
}

.day-header {
    display: flex;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.day-slots {
    margin-top: 10px;
    padding-left: 25px;
}

.time-slot {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    gap: 10px;
}

.time-slot input[type="time"] {
    border: 1px solid #ddd;
    padding: 5px;
    border-radius: 4px;
}

.remove-slot-btn {
    margin-left: auto;
}


/* Custom Fields Styles */
.custom-field-row {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    transition: all 0.3s ease;
    position: relative;
}



.custom-field-row:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0,123,255,0.15);
}

.custom-field-row .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

.custom-field-row .btn-outline-danger {
    border-color: #dc3545;
    color: #dc3545;
}

.custom-field-row .btn-outline-danger:hover {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
}

#custom-fields-container {
    max-height: 400px;
    overflow-y: auto;
    padding-right: 5px;
}

#custom-fields-container::-webkit-scrollbar {
    width: 6px;
}

#custom-fields-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

#custom-fields-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

#custom-fields-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

@media (max-width: 768px) {
    .week-availability .row.g-2 {
        flex-direction: column;
    }

    .day-card {
        margin-bottom: 10px;
    }

    .custom-field-row .col-10,
    .custom-field-row .col-2 {
        flex: 0 0 100%;
        max-width: 100%;
    }

    .custom-field-row .col-2 {
        margin-top: 10px;
        text-align: center;
    }
}

/* Modern Calendar Event Styles */
.fc-event.event-primary {
    background: var(--bs-primary) !important;
    border: none !important;
    color: #ffffff !important;
    border-radius: 8px !important;
    padding: 4px 8px !important;
    font-weight: 600 !important;
    box-shadow: 0 2px 4px rgba(var(--bs-primary-rgb), 0.3) !important;
    transition: all 0.2s ease !important;
}

.fc-event.event-primary:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(var(--bs-primary-rgb), 0.4) !important;
}

.fc-event.appointment-booking {
    background: var(--bs-success) !important;
    border: none !important;
    color: #ffffff !important;
    border-radius: 8px !important;
    padding: 4px 8px !important;
    font-weight: 600 !important;
    box-shadow: 0 2px 4px rgba(var(--bs-success-rgb), 0.3) !important;
    transition: all 0.2s ease !important;
}

.fc-event.appointment-booking:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(var(--bs-success-rgb), 0.4) !important;
}

.fc-event.appointment-booking-new {
    background: var(--bs-info) !important;
    border: none !important;
    color: #ffffff !important;
    border-radius: 8px !important;
    padding: 4px 8px !important;
    font-weight: 600 !important;
    box-shadow: 0 2px 4px rgba(var(--bs-info-rgb), 0.3) !important;
    transition: all 0.2s ease !important;
}

.fc-event.appointment-booking-new:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(var(--bs-info-rgb), 0.4) !important;
}

/* Calendar Legend */
.calendar-legend {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9em;
}

.legend-color {
    width: 16px;
    height: 16px;
    border-radius: 3px;
}

.legend-event {
    background-color: #51459d;
}

.legend-booking {
    background-color: #28a745;
}

.legend-appointment {
    background-color: #17a2b8;
}

/* Modern Calendar Styles */
.modern-calendar-card {
    background: var(--bs-primary);
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    border: none;
    position: relative;
}

.modern-calendar-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255,255,255,0.1);
    pointer-events: none;
}

.modern-calendar-header {
    padding: 30px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.calendar-title-section {
    display: flex;
    align-items: center;
    gap: 15px;
}

.calendar-icon-wrapper {
    width: 50px;
    height: 50px;
    background: var(--bs-primary);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    box-shadow: 0 8px 16px rgba(var(--bs-primary-rgb), 0.3);
}

.calendar-title {
    margin: 0;
    font-size: 24px;
    font-weight: 700;
    color: var(--bs-primary);
}

.calendar-subtitle {
    margin: 0;
    font-size: 14px;
    color: var(--bs-secondary);
    font-weight: 500;
}

.modern-legend {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.legend-item-modern {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 25px;
    font-size: 13px;
    font-weight: 500;
    color: var(--bs-dark);
    backdrop-filter: blur(10px);
    border: 1px solid var(--bs-border-color);
}

.legend-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.events-dot {
    background: var(--bs-primary);
}

.bookings-dot {
    background: var(--bs-success);
}

.modern-calendar-body {
    /* padding: 30px; */
    background: white;
    position: relative;
}

/* Modern FullCalendar Styling */
.modern-fullcalendar {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.modern-fullcalendar .fc-header-toolbar {
    padding: 20px;
    background: var(--bs-light);
    border-bottom: 1px solid var(--bs-border-color);
    margin-bottom: 0 !important;
}

.modern-fullcalendar .fc-toolbar-title {
    font-size: 20px !important;
    font-weight: 700 !important;
    color: var(--bs-dark) !important;
}

.modern-fullcalendar .fc-button-primary {
    background: #0CAF60 !important;
    border: none !important;
    border-radius: 10px !important;
    padding: 8px 16px !important;
    font-weight: 600 !important;
    box-shadow: 0 4px 6px rgba(var(--bs-primary-rgb), 0.3) !important;
    transition: all 0.3s ease !important;
}

.modern-fullcalendar .fc-button-primary:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 12px rgba(var(--bs-primary-rgb), 0.4) !important;
    background: var(--bs-primary) !important;
    filter: brightness(1.1);
}

.modern-fullcalendar .fc-button-primary:not(:disabled):active {
    background: var(--bs-primary) !important;
    filter: brightness(0.9);
}

.modern-fullcalendar .fc-daygrid-day {
    border: 1px solid var(--bs-border-color) !important;
    transition: all 0.2s ease !important;
}

.modern-fullcalendar .fc-daygrid-day:hover {
    background-color: var(--bs-light) !important;
}

.modern-fullcalendar .fc-daygrid-day-number {
    color: var(--bs-dark) !important;
    font-weight: 600 !important;
    padding: 8px !important;
}

.modern-fullcalendar .fc-day-today {
    background: rgba(var(--bs-primary-rgb), 0.1) !important;
    border: 2px solid var(--bs-primary) !important;
}

.modern-fullcalendar .fc-day-today .fc-daygrid-day-number {
    color: var(--bs-primary) !important;
    font-weight: 700 !important;
}

/* Modern Sidebar Styles */
.modern-sidebar-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    border: none;
    height: 100%;
}

.sidebar-header {
    padding: 12px;
    background: var(--bs-success);
    color: white;
    position: relative;
}

.sidebar-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255,255,255,0.1);
    pointer-events: none;
}

.date-display {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
}

.date-title {
    margin: 0;
    font-size: 20px;
    font-weight: 700;
    color: white;
}

.date-subtitle {
    margin: 0;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

.icon-circle {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.sidebar-body {
    padding: 30px;
}

.empty-state {
    text-align: center;
    padding: 40px 20px;
}

.empty-icon {
    width: 80px;
    height: 80px;
    background: var(--bs-light);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 36px;
    color: var(--bs-secondary);
}

.empty-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--bs-dark);
    margin-bottom: 8px;
}

.empty-text {
    font-size: 14px;
    color: var(--bs-secondary);
    margin: 0;
}

.events-container {
    max-height: 400px;
    overflow-y: auto;
}

/* Responsive Modern Calendar */
@media (max-width: 991.98px) {
    .modern-calendar-header {
        padding: 20px;
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .calendar-title {
        font-size: 20px;
    }

    .modern-calendar-body {
        padding: 20px;
    }

    .sidebar-header {
        padding: 20px;
    }

    .sidebar-body {
        padding: 20px;
    }
}

@media (max-width: 767.98px) {
    .modern-calendar-header {
        padding: 15px;
    }

    .calendar-title-section {
        gap: 10px;
    }

    .calendar-icon-wrapper {
        width: 40px;
        height: 40px;
        font-size: 20px;
    }

    .calendar-title {
        font-size: 18px;
    }

    .modern-legend {
        gap: 10px;
    }

    .legend-item-modern {
        padding: 6px 12px;
        font-size: 12px;
    }

    .modern-calendar-body {
        padding: 15px;
    }

    .modern-fullcalendar .fc-header-toolbar {
        padding: 15px;
        flex-direction: column;
        gap: 10px;
    }

    .modern-fullcalendar .fc-toolbar-title {
        font-size: 18px !important;
    }

    .modern-fullcalendar .fc-button-primary {
        padding: 6px 12px !important;
        font-size: 12px !important;
    }

    .sidebar-header {
        padding: 15px;
    }

    .date-title {
        font-size: 18px;
    }

    .icon-circle {
        width: 40px;
        height: 40px;
        font-size: 20px;
    }

    .sidebar-body {
        padding: 15px;
    }

    .empty-state {
        padding: 30px 15px;
    }

    .empty-icon {
        width: 60px;
        height: 60px;
        font-size: 28px;
    }
}

.copy-event-toast {
    position: fixed;
    bottom: 32px;
    left: 50%;
    transform: translateX(-50%);
    min-width: 220px;
    max-width: 90vw;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 24px rgba(0,0,0,0.12);
    border: 1px solid #e0e0e0;
    z-index: 2000;
    padding: 0;
    animation: fadeInUp 0.3s;
}
.copy-event-toast .toast-body {
    padding: 14px 18px;
    font-size: 1rem;
    color: #222;
}
@media (max-width: 600px) {
    .copy-event-toast {
        bottom: 16px;
        min-width: 160px;
        font-size: 0.95em;
    }
    .copy-event-toast .toast-body {
        padding: 10px 12px;
    }
}
@keyframes fadeInUp {
    from { opacity: 0; transform: translateX(-50%) translateY(30px); }
    to { opacity: 1; transform: translateX(-50%) translateY(0); }
}

.copy-link-dark-green {
    background-color: #176b3a !important;
    border-color: #176b3a !important;
    color: #fff !important;
}

.btn-nav-gradient {
    
    border: none;
    padding: 8px 16px;
    border-radius: 8px;
    display: inline-flex;
    align-items: center;
    font-weight: 600;
    margin-right: 8px;
    transition: background 0.2s, color 0.2s, transform 1.18s cubic-bezier(.4,1.4,.6,1);
}
.btn-nav-gradient:last-child { margin-right: 0; }
.btn-nav-gradient.active, .btn-nav-gradient:active {
    
    
    box-shadow: 0 2px 8px rgba(23,107,58,0.08);
}
.btn-nav-gradient:hover, .btn-nav-gradient:focus {
    transform: scale(1.07);
    z-index: 2;
}
@media (max-width: 600px) {
    .btn-nav-gradient {
        padding: 7px 10px;
        font-size: 0.98em;
    }
}

/* Location Selection Styles */
#selected-locations-container {
    min-height: 0;
}

#selected-locations-container .location-chip {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 20px;
    padding: 8px 16px;
    margin: 4px 8px 4px 0;
    display: inline-flex;
    align-items: center;
    font-size: 14px;
    transition: all 0.2s ease;
}

#selected-locations-container .location-chip:hover {
    background: #e9ecef;
    border-color: #adb5bd;
}

#selected-locations-container .location-chip .btn-link {
    text-decoration: none;
    padding: 0;
    margin-left: 8px;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

#selected-locations-container .location-chip .btn-link:hover {
    background: rgba(220, 53, 69, 0.1);
}

.dropdown-menu .dropdown-item {
    padding: 12px 16px;
    border-bottom: 1px solid #f8f9fa;
}

.dropdown-menu .dropdown-item:last-child {
    border-bottom: none;
}

.dropdown-menu .dropdown-item:hover {
    background-color: #f8f9fa;
}

.dropdown-menu .dropdown-item i {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    flex-shrink: 0;
}

.dropdown-menu .dropdown-item div {
    flex-grow: 1;
}

.dropdown-menu .dropdown-item strong {
    font-weight: 600;
    color: #212529;
}

.dropdown-menu .dropdown-item small {
    font-size: 12px;
    color: #6c757d;
}

/* Location Modal Styles */
.modal .form-select {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 8px 12px;
}

.modal .form-control {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 12px;
}

.modal .form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.modal .btn-success {
    background: linear-gradient(135deg, #198754 0%, #20c997 100%);
    border: none;
    border-radius: 8px;
    padding: 10px 20px;
    font-weight: 600;
    transition: all 0.2s ease;
}

.modal .btn-success:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(25, 135, 84, 0.3);
}

.modal .btn-secondary {
    background: #6c757d;
    border: none;
    border-radius: 8px;
    padding: 10px 20px;
    font-weight: 600;
}

/* Custom Redirect URL Styles */
#custom_redirect_url {
    transition: all 0.3s ease;
}

#custom_redirect_url:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    transform: translateY(-1px);
}

#custom_redirect_url:valid {
    border-color: #198754;
}

#custom_redirect_url:invalid:not(:placeholder-shown) {
    border-color: #dc3545;
}

.input-group-text {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    color: #6c757d;
    transition: all 0.3s ease;
}

#custom_redirect_url:focus + .input-group-text,
.input-group:focus-within .input-group-text {
    border-color: #0d6efd;
    background: rgba(13, 110, 253, 0.1);
    color: #0d6efd;
}

.form-text {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 0.5rem;
}

.form-text i {
    color: #0d6efd;
}

/* Clickable Location Styles */
.clickable-location {
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    border-radius: 4px !important;
    padding: 2px 4px !important;
}

.clickable-location:hover {
    background-color: rgba(0, 123, 255, 0.1) !important;
    transform: translateY(-1px) !important;
}

.location-icon {
    margin-right: 8px !important;
    font-size: 1.1em !important;
}

.location-hint {
    font-size: 0.85em !important;
    color: #6c757d !important;
    margin-left: 8px !important;
}

/* Modern White Layout Styles */
.icon-wrapper-modern {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

/* Modern Meeting Locations - Single Line Layout */
.modern-locations-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
}

.modern-location-chip {
    display: inline-flex !important;
    align-items: center !important;
    padding: 8px 16px !important;
    background: #f8f9fa !important;
    border: 1px solid #e9ecef !important;
    border-radius: 25px !important;
    text-decoration: none !important;
    transition: all 0.3s ease !important;
    font-size: 0.9rem !important;
    font-weight: 500 !important;
    color: #495057 !important;
    white-space: nowrap !important;
}

.modern-location-chip:hover {
    background: #e9ecef !important;
    border-color: #6c757d !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1) !important;
    color: #212529 !important;
}

.modern-location-chip .location-icon {
    font-size: 1.1em !important;
    margin-right: 8px !important;
    width: auto !important;
}

.modern-location-chip .location-name {
    font-weight: 600 !important;
}

/* Location type specific colors */
/* .modern-location-chip.zoom-chip { border-left: 3px solid #2D8CFF !important; }
.modern-location-chip.zoom-chip:hover { border-left-color: #0066CC !important; }

.modern-location-chip.meet-chip { border-left: 3px solid #34A853 !important; }
.modern-location-chip.meet-chip:hover { border-left-color: #137333 !important; }

.modern-location-chip.skype-chip { border-left: 3px solid #00AFF0 !important; }
.modern-location-chip.skype-chip:hover { border-left-color: #0078D4 !important; }

.modern-location-chip.phone-chip { border-left: 3px solid #FF6B35 !important; }
.modern-location-chip.phone-chip:hover { border-left-color: #E55100 !important; }

.modern-location-chip.others-chip { border-left: 3px solid #6C757D !important; }
.modern-location-chip.others-chip:hover { border-left-color: #495057 !important; } */

/* Address content styling */
.address-content p {
    line-height: 1.5;
    font-size: 0.95rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .modern-locations-list {
        flex-direction: column;
        align-items: stretch;
        gap: 6px;
    }

    .modern-location-chip {
        justify-content: flex-start !important;
        border-radius: 12px !important;
        padding: 10px 16px !important;
    }

    .icon-wrapper-modern {
        width: 36px;
        height: 36px;
    }
}
    </style>
@endpush

@php
    $setting = \App\Models\Utility::settings();
@endphp

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{route('dashboard')}}">{{__('Dashboard')}}</a></li>
    <li class="breadcrumb-item">{{__('Smart Scheduler')}}</li>
@endsection

@section('content')
<!-- Create Event Modal -->
<div class="modal fade" id="createEventModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('Create New Event') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="createEventForm">
                <div class="modal-body">
                    @csrf
                    <!-- PART 1: Event Details -->
                    <div class="form-part">
                        <div class="part-title">{{ __('Event Details') }}</div>
                        <div class="row">
                            <div class="col-12 mb-3">
                                <label for="event_title" class="form-label">{{ __(' Title') }} *</label>
                                <input type="text" class="form-control" id="event_title" name="title" required>
                            </div>
                        </div>

                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="event_duration" class="form-label">{{ __('Duration (Minutes)') }}</label>
                                <input type="number" class="form-control" id="event_duration" name="duration" min="1" max="1440" value="60">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="booking_per_slot" class="form-label">{{ __('Booking Per Slot') }}</label>
                                <input type="number" class="form-control" id="booking_per_slot" name="booking_per_slot" min="1" max="100" value="1">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="minimum_notice_value" class="form-label text-primary fw-semibold">{{ __('Minimum Scheduling Notice') }}</label>
                                <div class="d-flex gap-2 align-items-stretch">
                                    <div class="flex-fill">
                                        <input type="number" class="form-control border-primary" id="minimum_notice_value" name="minimum_notice_value" min="0" max="999" value="0" placeholder="0" style="border-width: 2px;">
                                    </div>
                                    <div class="flex-fill">
                                        <select class="form-control border-primary" id="minimum_notice_unit" name="minimum_notice_unit" style="border-width: 2px;">
                                            <option value="minutes">{{ __('Minutes') }}</option>
                                            <option value="hours">{{ __('Hours') }}</option>
                                            <option value="days">{{ __('Days') }}</option>
                                            <option value="months">{{ __('Months') }}</option>
                                        </select>
                                    </div>
                                </div>
                                <small class="text-muted mt-1" id="minimum_notice_preview">No minimum notice required</small>
                                <!-- Hidden field to store the calculated minutes for backend compatibility -->
                                <input type="hidden" id="minimum_notice" name="minimum_notice" value="0">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="event_description" class="form-label">{{ __('Description') }}</label>
                            <textarea class="form-control" id="event_description" name="description" rows="3"></textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-12 mb-3">
                                <label for="event_location" class="form-label">{{ __('Location') }} <span class="text-danger">*</span></label>

                                <!-- Selected Locations Display -->
                                <div id="selected-locations-container" class="mb-2">
                                    <!-- Selected location chips will appear here -->
                                </div>

                                <!-- Location Dropdown -->
                                <div class="dropdown">
                                    <button class="btn btn-outline-secondary dropdown-toggle w-100 text-start" type="button" id="locationDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="ti ti-map-pin me-2"></i>{{ __('Add a location') }}
                                    </button>
                                    <ul class="dropdown-menu w-100" aria-labelledby="locationDropdown">
                                        <li>
                                            <a class="dropdown-item" href="#" onclick="openLocationModal('zoom')">
                                                <i class="ti ti-video me-2 text-primary"></i>
                                                <div>
                                                    <strong>{{ __('Zoom') }}</strong>
                                                    <br><small class="text-muted">{{ __('Online meeting/conference') }}</small>
                                                </div>
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="#" onclick="openLocationModal('in_person')">
                                                <i class="ti ti-map-pin me-2 text-success"></i>
                                                <div>
                                                    <strong>{{ __('In-person meeting') }}</strong>
                                                    <br><small class="text-muted">{{ __('Set an address or place') }}</small>
                                                </div>
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="#" onclick="openLocationModal('phone')">
                                                <i class="ti ti-phone me-2 text-info"></i>
                                                <div>
                                                    <strong>{{ __('Phone call') }}</strong>
                                                    <br><small class="text-muted">{{ __('Incoming or Outgoing calls') }}</small>
                                                </div>
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="#" onclick="openLocationModal('meet')">
                                                <i class="ti ti-brand-google me-2 text-warning"></i>
                                                <div>
                                                    <strong>{{ __('Google Meet') }}</strong>
                                                    <br><small class="text-muted">{{ __('Online meeting/conference') }}</small>
                                                </div>
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="#" onclick="openLocationModal('skype')">
                                                <i class="ti ti-brand-skype me-2 text-primary"></i>
                                                <div>
                                                    <strong>{{ __('Skype') }}</strong>
                                                    <br><small class="text-muted">{{ __('Online meeting/conference') }}</small>
                                                </div>
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="#" onclick="openLocationModal('others')">
                                                <i class="ti ti-dots me-2 text-secondary"></i>
                                                <div>
                                                    <strong>{{ __('Others') }}</strong>
                                                    <br><small class="text-muted">{{ __('Custom location type') }}</small>
                                                </div>
                                            </a>
                                        </li>
                                    </ul>
                                </div>

                                <!-- Hidden input to store location data -->
                                <input type="hidden" id="event_locations_data" name="locations_data" value="">
                            </div>
                        </div>

                        <!-- Custom Redirect URL Section -->
                        <div class="row">
                            <div class="col-12 mb-3">
                                <label for="custom_redirect_url" class="form-label">
                                    <i class="ti ti-external-link me-2"></i>{{ __('Custom Redirect URL') }}
                                    <small class="text-muted">({{ __('Optional') }})</small>
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="ti ti-link"></i>
                                    </span>
                                    <input type="url"
                                           class="form-control"
                                           id="custom_redirect_url"
                                           name="custom_redirect_url"
                                           placeholder="{{ __('https://example.com/thank-you') }}"
                                           pattern="https?://.*">
                                </div>
                                <div class="form-text">
                                    <i class="ti ti-info-circle me-1"></i>
                                    {{ __('If provided, users will be redirected to this URL after booking confirmation instead of the default confirmation page.') }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-part">
    <div class="part-title">{{ __('Additional Information') }}</div>

    <div class="row">
        <div class="col-md-6 mb-3">
            <label for="custom_field" class="form-label">{{ __('Field Type') }}</label>
            <select class="form-control" id="custom_field" name="custom_field[]" onchange="toggleCustomField()">
                <option value="">{{ __('No Custom Field') }}</option>
                <option value="contact_type">{{ __('Contact Type') }}</option>
                <option value="date_of_birth">{{ __('Date of Birth') }}</option>
                <option value="business_type">{{ __('Business Type') }}</option>
                <option value="business_gst_number">{{ __('Business GST Number') }}</option>
                <option value="lead_value">{{ __('Lead Value') }}</option>
                <option value="assigned_to_staff">{{ __('Assigned to staff') }}</option>
                <option value="contact_source">{{ __('Contact Source') }}</option>
                <option value="opportunity_name">{{ __('Opportunity Name') }}</option>
                <option value="postal_code">{{ __('Postal Code') }}</option>
                <option value="full_name">{{ __('Full Name') }}</option>
                <option value="specific_requirement">{{ __('Any Specific Requirement') }}</option>
                <option value="used_whatsapp_api_chatbots">{{ __('Have you used Whatsapp Api and Chatbots ever') }}</option>
                <option value="generate_leads">{{ __('How do you generate leads') }}</option>
                <option value="hear_about_omx_sales">{{ __('Where did you hear about OMX Sales?') }}</option>
                <option value="city">{{ __('City') }}</option>
                <option value="have_msme_certificate">{{ __('Do you have MSME Certificate?') }}</option>
                <option value="whatsapp_number">{{ __('Whatsapp Number') }}</option>
                <option value="meta_business_name">{{ __('META Business Name') }}</option>
                <option value="have_website">{{ __('Do you have a website?') }}</option>
                <option value="business_industry">{{ __('Business industry') }}</option>
                <option value="message">{{ __('Message') }}</option>
                <option value="organization_task">{{ __('Organization Task') }}</option>
                <option value="team_size">{{ __('Team Size') }}</option>
                <option value="company_revenue">{{ __('Company Revenue') }}</option>
                <option value="budget">{{ __('What is your budget?') }}</option>
                <option value="real_estate_services">{{ __('What type of real estate services do you offer? (Residential, commercial, etc)') }}</option>
                <option value="using_chatbot_tools">{{ __('Are you currently using any chatbot or automation tools in your business?') }}</option>
                <option value="implement_chatbot_timeframe">{{ __('How soon are you looking to implement a chatbot for your business?') }}</option>
                <option value="running_digital_ads">{{ __('Are you currently running any digital ads for your real estate projects?') }}</option>
                <option value="monthly_advertising_budget">{{ __('Monthly Advertising Budget?') }}</option>
                <option value="promoted_projects_count">{{ __('How many real estate projects are you promoting right now?') }}</option>
                <option value="biggest_marketing_challenge">{{ __('What is your biggest marketing challenge right now?') }}</option>
                <option value="property_price_range">{{ __('What is the price range of the property you are selling?') }}</option>
                <option value="using_crm_software">{{ __('Are you currently using any software or tools for lead management or CRM?') }}</option>
                <option value="advertising_on_third_party_platforms">{{ __('Are you advertising on any third-party real estate platforms?') }}</option>
                <option value="know_whatsapp_api">{{ __('Do you know about the WhatsApp API?') }}</option>
                <option value="messages_volume">{{ __('How many messages do you need to send?') }}</option>
                <option value="using_whatsapp_official_api">{{ __('How are you currently doing WhatsApp Official API?') }}</option>
                <option value="monthly_lead_sales_volume">{{ __('Monthly Lead/Sales Volume') }}</option>
            </select>
        </div>
        <div class="col-md-6 mb-3 d-flex align-items-end">
            <button type="button" class="btn btn-primary" onclick="addCustomField()">
                <i class="ti ti-plus me-1"></i>{{ __('Add Field') }}
            </button>
        </div>
    </div>

    <!-- Dynamic Custom Fields Container -->
    <div id="custom-fields-container">
        <!-- Multiple custom fields will be added here dynamically -->
    </div>
</div>
                    <!-- PART 2: Weekly Availability -->
    <!-- PART 2: Weekly Availability -->
<!-- PART 2: Weekly Availability -->
<div class="form-part">
    <div class="part-title">{{ __('Weekly Availability') }}</div>
    
    <div class="week-availability-vertical">
        <!-- Monday -->
        <div class="day-availability">
            <div class="day-header">
                <input type="checkbox" id="monday-checkbox" name="availability[monday][enabled]" checked>
                <label for="monday-checkbox">{{ __('Monday') }}</label>
                <button type="button" class="btn btn-sm btn-outline-primary ms-2 add-slot-btn" data-day="monday" onclick="addTimeSlot('monday')">
                    <i class="ti ti-plus"></i> Add Slot
                </button>
            </div>
            <div class="day-slots" id="monday-slots">
                <div class="time-slot">
                    <input type="time" name="availability[monday][slots][0][start]" class="form-control" value="09:00" required>
                    <span class="mx-2">to</span>
                    <input type="time" name="availability[monday][slots][0][end]" class="form-control" value="17:00" required>
                    <button type="button" class="btn btn-sm btn-outline-danger remove-slot-btn" onclick="removeTimeSlot('monday', 0)">
                        <i class="ti ti-trash"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Tuesday -->
        <div class="day-availability">
            <div class="day-header">
                <input type="checkbox" id="tuesday-checkbox" name="availability[tuesday][enabled]" checked>
                <label for="tuesday-checkbox">{{ __('Tuesday') }}</label>
                <button type="button" class="btn btn-sm btn-outline-primary ms-2 add-slot-btn" data-day="tuesday" onclick="addTimeSlot('tuesday')">
                    <i class="ti ti-plus"></i> Add Slot
                </button>
            </div>
            <div class="day-slots" id="tuesday-slots">
                <div class="time-slot">
                    <input type="time" name="availability[tuesday][slots][0][start]" class="form-control" value="09:00" required>
                    <span class="mx-2">to</span>
                    <input type="time" name="availability[tuesday][slots][0][end]" class="form-control" value="17:00" required>
                    <button type="button" class="btn btn-sm btn-outline-danger remove-slot-btn" onclick="removeTimeSlot('tuesday', 0)">
                        <i class="ti ti-trash"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Wednesday -->
        <div class="day-availability">
            <div class="day-header">
                <input type="checkbox" id="wednesday-checkbox" name="availability[wednesday][enabled]" checked>
                <label for="wednesday-checkbox">{{ __('Wednesday') }}</label>
                <button type="button" class="btn btn-sm btn-outline-primary ms-2 add-slot-btn" data-day="wednesday" onclick="addTimeSlot('wednesday')">
                    <i class="ti ti-plus"></i> Add Slot
                </button>
            </div>
            <div class="day-slots" id="wednesday-slots">
                <div class="time-slot">
                    <input type="time" name="availability[wednesday][slots][0][start]" class="form-control" value="09:00" required>
                    <span class="mx-2">to</span>
                    <input type="time" name="availability[wednesday][slots][0][end]" class="form-control" value="17:00" required>
                    <button type="button" class="btn btn-sm btn-outline-danger remove-slot-btn" onclick="removeTimeSlot('wednesday', 0)">
                        <i class="ti ti-trash"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Thursday -->
        <div class="day-availability">
            <div class="day-header">
                <input type="checkbox" id="thursday-checkbox" name="availability[thursday][enabled]" checked>
                <label for="thursday-checkbox">{{ __('Thursday') }}</label>
                <button type="button" class="btn btn-sm btn-outline-primary ms-2 add-slot-btn" data-day="thursday" onclick="addTimeSlot('thursday')">
                    <i class="ti ti-plus"></i> Add Slot
                </button>
            </div>
            <div class="day-slots" id="thursday-slots">
                <div class="time-slot">
                    <input type="time" name="availability[thursday][slots][0][start]" class="form-control" value="09:00" required>
                    <span class="mx-2">to</span>
                    <input type="time" name="availability[thursday][slots][0][end]" class="form-control" value="17:00" required>
                    <button type="button" class="btn btn-sm btn-outline-danger remove-slot-btn" onclick="removeTimeSlot('thursday', 0)">
                        <i class="ti ti-trash"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Friday -->
        <div class="day-availability">
            <div class="day-header">
                <input type="checkbox" id="friday-checkbox" name="availability[friday][enabled]" checked>
                <label for="friday-checkbox">{{ __('Friday') }}</label>
                <button type="button" class="btn btn-sm btn-outline-primary ms-2 add-slot-btn" data-day="friday" onclick="addTimeSlot('friday')">
                    <i class="ti ti-plus"></i> Add Slot
                </button>
            </div>
            <div class="day-slots" id="friday-slots">
                <div class="time-slot">
                    <input type="time" name="availability[friday][slots][0][start]" class="form-control" value="09:00" required>
                    <span class="mx-2">to</span>
                    <input type="time" name="availability[friday][slots][0][end]" class="form-control" value="17:00" required>
                    <button type="button" class="btn btn-sm btn-outline-danger remove-slot-btn" onclick="removeTimeSlot('friday', 0)">
                        <i class="ti ti-trash"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Saturday -->
        <div class="day-availability">
            <div class="day-header">
                <input type="checkbox" id="saturday-checkbox" name="availability[saturday][enabled]" checked>
                <label for="saturday-checkbox">{{ __('Saturday') }}</label>
                <button type="button" class="btn btn-sm btn-outline-primary ms-2 add-slot-btn" data-day="saturday" onclick="addTimeSlot('saturday')">
                    <i class="ti ti-plus"></i> Add Slot
                </button>
            </div>
            <div class="day-slots" id="saturday-slots">
                <div class="time-slot">
                    <input type="time" name="availability[saturday][slots][0][start]" class="form-control" value="09:00" required>
                    <span class="mx-2">to</span>
                    <input type="time" name="availability[saturday][slots][0][end]" class="form-control" value="17:00" required>
                    <button type="button" class="btn btn-sm btn-outline-danger remove-slot-btn" onclick="removeTimeSlot('saturday', 0)">
                        <i class="ti ti-trash"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Sunday -->
        <div class="day-availability">
            <div class="day-header">
                <input type="checkbox" id="sunday-checkbox" name="availability[sunday][enabled]" checked>
                <label for="sunday-checkbox">{{ __('Sunday') }}</label>
                <button type="button" class="btn btn-sm btn-outline-primary ms-2 add-slot-btn" data-day="sunday" onclick="addTimeSlot('sunday')">
                    <i class="ti ti-plus"></i> Add Slot
                </button>
            </div>
            <div class="day-slots" id="sunday-slots">
                <div class="time-slot">
                    <input type="time" name="availability[sunday][slots][0][start]" class="form-control" value="09:00" required>
                    <span class="mx-2">to</span>
                    <input type="time" name="availability[sunday][slots][0][end]" class="form-control" value="17:00" required>
                    <button type="button" class="btn btn-sm btn-outline-danger remove-slot-btn" onclick="removeTimeSlot('sunday', 0)">
                        <i class="ti ti-trash"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
    

<div class="mb-4">
    <label for="override_date" class="form-label">{{ __('Override Date and Time') }}</label>
    
    <div class="input-group mb-2">
        <input type="datetime-local" class="form-control" id="override_date">
        <button type="button" class="btn btn-primary" onclick="addUnavailableSlot()">
            {{ __('Add Slot') }}
        </button>
    </div>

    <div id="unavailable-slots-list" class="mt-3">
        <!-- Dynamically added slots go here -->
    </div>
</div>

                                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                    <button type="submit" class="btn btn-primary" id="submitBtn">{{ __('Create Event') }}</button>
                </div>
            </form>
        </div>
    </div>
</div>
</div>
<!-- View Event Modal -->
<div class="modal fade" id="viewEventModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="ti ti-calendar-event me-2"></i>{{ __('Event Details') }}
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body p-4">
                <!-- Event Title -->
                <div class="text-center mb-4 pb-3 border-bottom">
                    <h3 class="text-primary mb-1" id="view-event-title"></h3>
                    <small class="text-muted">Event Information</small>
                </div>
                
                <!-- Event Details Grid -->
                <div class="row g-4">
                
                    <!-- Date & Time Section -->

                    
                    <!-- Event Settings Section -->
                    <div class="col-12">
                        <h6 class="text-secondary mb-3">
                            <i class="ti ti-settings me-2"></i>Event Settings
                        </h6>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="text-center p-3 border rounded">
                                    <i class="ti ti-clock text-info fs-3 mb-2"></i>
                                    <div class="fw-bold text-dark">Duration</div>
                                    <div class="text-muted" id="view-event-duration"></div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center p-3 border rounded">
                                    <i class="ti ti-users text-warning fs-3 mb-2"></i>
                                    <div class="fw-bold text-dark">Booking Slots</div>
                                    <div class="text-muted" id="view-event-booking-slots"></div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center p-3 border rounded">
                                    <i class="ti ti-bell text-secondary fs-3 mb-2"></i>
                                    <div class="fw-bold text-dark">Notice Required</div>
                                    <div class="text-muted" id="view-event-notice"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Description Section -->
                    <div class="col-12" id="view-description-row" style="display: none;">
                        <h6 class="text-secondary mb-3">
                            <i class="ti ti-file-text me-2"></i>Description
                        </h6>
                        <div class="p-3 bg-light rounded">
                            <div id="view-event-description" class="text-dark"></div>
                        </div>
                    </div>
                    
                    <!-- Location Section -->
                    <div class="col-12" id="view-location-section" style="display: none;">
                        <div class="d-flex align-items-center mb-4">
                            <div class="icon-wrapper-modern me-3">
                                <i class="ti ti-map-pin text-primary"></i>
                            </div>
                            <div>
                                <h5 class="fw-bold text-dark mb-0">Location Details</h5>
                                <small class="text-muted">Where this event will take place</small>
                            </div>
                        </div>

                        <!-- Locations Container with Dynamic Column Layout -->
                        <div class="row g-3" id="view-locations-container">
                            <!-- Physical Address -->
                            <div id="view-address-row" style="display: none;">
                                <div class="p-4 border rounded bg-white shadow-sm h-100">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="flex-shrink-0">
                                            <div class="icon-wrapper-modern">
                                                <i class="ti ti-map-pin text-success fs-5"></i>
                                            </div>
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                            <h6 class="fw-bold text-dark mb-0">Physical Address</h6>
                                            <small class="text-muted">Meeting location</small>
                                        </div>
                                    </div>
                                    <div class="address-content">
                                        <p class="mb-0 text-dark" id="view-event-address"></p>
                                    </div>
                                </div>
                            </div>

                            <!-- Meeting Locations -->
                            <div id="view-link-row" style="display: none;">
                                <div class="p-4 border rounded bg-white shadow-sm h-100">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="flex-shrink-0">
                                            <div class="icon-wrapper-modern">
                                                <i class="ti ti-video text-primary fs-5"></i>
                                            </div>
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                            <h6 class="fw-bold text-dark mb-0">Meeting Locations</h6>
                                            <small class="text-muted">Click to join</small>
                                        </div>
                                    </div>
                                    <div id="view-meeting-locations-container" class="modern-locations-list">
                                        <!-- Multiple meeting locations will be displayed here -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Legacy Location Type Display (for backward compatibility) -->
                        <div class="mb-3" id="view-location-row" style="display: none;">
                            <div class="d-flex align-items-center p-3 border rounded">
                                <div class="flex-shrink-0">
                                    <i class="ti ti-map-pin text-success fs-4"></i>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <div class="fw-bold text-dark">Location Type</div>
                                    <div class="text-muted" id="view-event-location"></div>
                                </div>
                            </div>
                        </div>
                </div>
                </div>
            </div>
            <div class="modal-footer bg-light">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="ti ti-x me-1"></i>{{ __('Close') }}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modern Calendar Section -->
<div class="row" id="calendar-section">
    <div class="col-xl-9 col-lg-8 col-md-12 mb-4">
        <div class="modern-calendar-card">
            <div class="modern-calendar-header">
                <div class="calendar-title-section">
                    <div class="calendar-icon-wrapper bg-primary">
                        <i class="fas fa-address-card"></i>
                    </div>
                    <div>
                        <!-- <h4 class="calendar-title">{{ __('Calendar') }}</h4> -->
                        <h5>{{ __('Manage your Appointments') }}</h5>
                    </div>
                </div>
                <!-- Modern Legend -->
                <div class="modern-legend">

                    <div class="legend-item-modern">
                        <div class="legend-dot bookings-dot"></div>
                        <span>{{ __('Bookings') }}</span>
                    </div>
                </div>
            </div>
            <div class="modern-calendar-body">
                <div id='calendar' class="modern-fullcalendar"></div>
            </div>
        </div>
    </div>

    <!-- Modern Sidebar -->
    <div class="col-xl-3 col-lg-4 col-md-12 mb-4">
        <div class="modern-sidebar-card">
            <div class="sidebar-header">
                <div class="date-display">
                    <div class="current-date">
                        <h5 class="date-title" id="selected-date-title">{{ __('Today') }}</h5>
                        <p class="date-subtitle" id="selected-date-subtitle">{{ date('F j, Y') }}</p>
                    </div>
                    <div class="date-icon">
                        <div class="icon-circle">
                            <i class="ti ti-calendar-time"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="sidebar-body">
                <div id="selected-date-events">
                    <div class="empty-state" id="no-events-message">
                        <div class="empty-icon">
                            <i class="ti ti-calendar-off"></i>
                        </div>
                        <h6 class="empty-title">{{ __('No Events') }}</h6>
                        <p class="empty-text">{{ __('Click on a date to view events') }}</p>
                    </div>
                    <div class="events-container" id="events-list" style="display: none;">
                        <!-- Events will be populated here -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Events Section -->
<div class="row" id="events-section" style="display: none;">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">{{ __('Events List') }}</h5>
                <div class="action-buttons">
                    <button class="id="create-event-btn" onclick="openCreateEventModal()" style="background: linear-gradient(to right, #0f5132, #0d6efd); color: white; border: none; padding: 12px 16px; border-radius: 8px; display: inline-flex; align-items: center; font-weight: 600;">
                        <i class="fa fa-plus-circle"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped" id="events-table">
                        <thead>
                            <tr>
                                <th>{{ __('Title') }}</th>
                                <th>{{ __('Date Range') }}</th>
                                <th>{{ __('Duration') }}</th>
                                <th>{{ __('Location') }}</th>
                                <th>{{ __('Status') }}</th>
                                <th>{{ __('Appointments') }}</th>
                                <th>{{ __('Actions') }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="8" class="text-center">{{ __('Loading events...') }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Appointment Section -->
<!-- <div class="row" id="appointment-section" style="display: none;">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{{ __('Appointments') }}</h5>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-sm btn-primary active" id="upcoming-btn" onclick="switchAppointmentView('upcoming')">
                            <i class="ti ti-clock me-1"></i>{{ __('Upcoming') }}
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-primary" id="past-btn" onclick="switchAppointmentView('past')">
                            <i class="ti ti-history me-1"></i>{{ __('Past') }}
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-primary" id="daterange-btn" onclick="switchAppointmentView('daterange')">
                            <i class="ti ti-calendar-range me-1"></i>{{ __('Date Range') }}
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-primary" id="canceled-btn" onclick="switchAppointmentView('canceled')">
                            <i class="ti ti-x me-1"></i>{{ __('Canceled') }}
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div id="appointment-content">
                    <div id="upcoming-appointments">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>{{ __('Title') }}</th>
                                        <th>{{ __('Date & Time') }}</th>
                                        <th>{{ __('Duration') }}</th>
                                        <th>{{ __('Status') }}</th>
                                        <th>{{ __('Actions') }}</th>
                                    </tr>
                                </thead>
                                <tbody id="upcoming-appointments-tbody">
                                    <tr>
                                        <td colspan="5" class="text-center text-muted">{{ __('Loading upcoming appointments...') }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div id="past-appointments" style="display: none;">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>{{ __('Title') }}</th>
                                        <th>{{ __('Date & Time') }}</th>
                                        <th>{{ __('Duration') }}</th>
                                        <th>{{ __('Status') }}</th>
                                        <th>{{ __('Actions') }}</th>
                                    </tr>
                                </thead>
                                <tbody id="past-appointments-tbody">
                                    <tr>
                                        <td colspan="5" class="text-center text-muted">{{ __('Loading past appointments...') }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div id="daterange-appointments" style="display: none;">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>{{ __('Title') }}</th>
                                        <th>{{ __('Date & Time') }}</th>
                                        <th>{{ __('Duration') }}</th>
                                        <th>{{ __('Status') }}</th>
                                        <th>{{ __('Actions') }}</th>
                                    </tr>
                                </thead>
                                <tbody id="daterange-appointments-tbody">
                                    <tr>
                                        <td colspan="5" class="text-center text-muted">{{ __('Select date range to view appointments') }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div id="canceled-appointments" style="display: none;">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>{{ __('Title') }}</th>
                                        <th>{{ __('Date & Time') }}</th>
                                        <th>{{ __('Duration') }}</th>
                                        <th>{{ __('Canceled Date') }}</th>
                                        <th>{{ __('Actions') }}</th>
                                    </tr>
                                </thead>
                                <tbody id="canceled-appointments-tbody">
                                    <tr>
                                        <td colspan="5" class="text-center text-muted">{{ __('Loading canceled appointments...') }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div> -->

<!-- Bookings Section -->
<div class="row" id="bookings-section" style="display: none;">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{{ __('All Appointments') }}</h5>
                    @can('create booking')
                    <div>
                        <button type="button" class="btn btn-outline-secondary" onclick="refreshBookings()">
                            <i class="ti ti-refresh me-1"></i>{{ __('Refresh') }}
                        </button>
                    </div>
                    @endcan
                </div>
            </div>
            <div class="card-body">
                <!-- Alert Messages -->
                <div id="bookings-alert-container" style="display: none;">
                    <div class="alert alert-dismissible fade show" role="alert" id="bookings-alert-message">
                        <span id="bookings-alert-text"></span>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                </div>

                <div class="table-responsive" style="overflow-x: auto; white-space: nowrap;">
                    <table class="table table-striped" id="bookings-table" style="min-width: 1200px;">
                        <thead>
                            <tr>
                                <th style="min-width: 60px;">{{ __('ID') }}</th>
                                <th style="min-width: 150px;">{{ __('Event Title') }}</th>
                                <th style="min-width: 120px;">{{ __('Name') }}</th>
                                <th style="min-width: 180px;">{{ __('Email') }}</th>
                                <th style="min-width: 120px;">{{ __('Phone') }}</th>
                                <th style="min-width: 100px;">{{ __('Date') }}</th>
                                <th style="min-width: 80px;">{{ __('Time') }}</th>
                                <th style="min-width: 150px;">{{ __('Custom Fields') }}</th>
                                <th style="min-width: 120px;">{{ __('Created At') }}</th>
                                @can('manage booking')
                                <th style="min-width: 100px;">{{ __('Actions') }}</th>
                                @endcan
                            </tr>
                        </thead>
                        <tbody id="bookings-tbody">
                            <tr>
                                <td colspan="{{ Auth::user() && Auth::user()->can('manage booking') ? '10' : '9' }}" class="text-center py-4">
                                    <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                                    {{ __('Loading bookings...') }}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Appointment Booking Modal -->
<div class="modal fade" id="appointmentBookingModal" tabindex="-1" aria-labelledby="appointmentBookingModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="appointmentBookingModalLabel">
                    <i class="ti ti-calendar-plus me-2"></i>{{ __('Book Appointment') }}
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="appointmentBookingForm">
                @csrf
                <div class="modal-body">
                    <div class="row">
                        <!-- Contact Name -->
                        <div class="col-md-6 mb-3">
                            <label for="appointment_contact_name" class="form-label">{{ __('Contact Name') }} *</label>
                            <input type="text" class="form-control" id="appointment_contact_name" name="contact_name" required>
                        </div>

                        <!-- Calendar Event -->
                        <div class="col-md-6 mb-3">
                            <label for="appointment_calendar_event" class="form-label">{{ __('Calendar Event') }} *</label>
                            <select class="form-control" id="appointment_calendar_event" name="calendar_event_id" required onchange="updateEventDetails()">
                                <option value="">{{ __('Select an event') }}</option>
                                <!-- Events will be populated dynamically -->
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Event Location Type -->
                        <div class="col-md-6 mb-3">
                            <label for="appointment_location_type" class="form-label">{{ __('Event Location') }}</label>
                            <input type="text" class="form-control" id="appointment_location_type" name="location_type" readonly>
                        </div>

                        <!-- Event Location Value -->
                        <div class="col-md-6 mb-3">
                            <label for="appointment_location_value" class="form-label">{{ __('Event Location Value') }}</label>
                            <input type="text" class="form-control" id="appointment_location_value" name="location_value" readonly>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Event Date -->
                        <div class="col-md-6 mb-3">
                            <label for="appointment_date" class="form-label">{{ __('Event Date') }} *</label>
                            <input type="date" class="form-control" id="appointment_date" name="event_date" required readonly>
                        </div>

                        <!-- Timezone Selection -->
                        <div class="col-md-6 mb-3">
                            <label for="appointment_timezone" class="form-label">{{ __('Timezone') }} *</label>
                            <select class="form-control" id="appointment_timezone" name="timezone" required>
                                <option value="">{{ __('Select timezone') }}</option>
                                <!-- Timezones will be populated by JavaScript -->
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Time Slot -->
                        <div class="col-md-12 mb-3">
                            <label for="appointment_timeslot" class="form-label">{{ __('Time Slot') }} *</label>
                            <select class="form-control" id="appointment_timeslot" name="timeslot" required>
                                <option value="">{{ __('Select a time slot') }}</option>
                                <!-- Time slots will be populated dynamically -->
                            </select>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="ti ti-x me-1"></i>{{ __('Cancel') }}
                    </button>
                    <button type="submit" class="btn btn-success" id="appointmentSubmitBtn">
                        <i class="ti ti-check me-1"></i>{{ __('Book Appointment') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Toast Notification for Copy Event Link -->
<div id="copy-event-toast" class="copy-event-toast" role="alert" aria-live="polite" aria-atomic="true" style="display:none;">
    <div class="toast-body d-flex align-items-center">
        <i class="ti ti-check-circle me-2 text-success fs-5"></i>
        <span id="copy-event-toast-message">Event link copied!</span>
        <button type="button" class="btn-close ms-auto" aria-label="Close" onclick="hideCopyEventToast()"></button>
    </div>
</div>

@endsection
@push('script-page')
<script>

//dayslots
// Function to toggle day slots visibility
function toggleDaySlots(day) {
    const slotsDiv = document.getElementById(day + '-slots');
    const dayCard = document.querySelector(`[data-day="${day}"]`);
    
    if (slotsDiv.style.display === 'none') {
        slotsDiv.style.display = 'block';
        dayCard.classList.add('active');
    } else {
        slotsDiv.style.display = 'none';
        dayCard.classList.remove('active');
    }
}

// Function to add time slot
// Function to add time slot
function addTimeSlot(day) {
    const slotsContainer = document.getElementById(day + '-slots');
    const slotCount = slotsContainer.children.length;
    
    const slotHtml = `
        <div class="time-slot">
            <input type="time" name="availability[${day}][slots][${slotCount}][start]" class="form-control" required>
            <span class="mx-2">to</span>
            <input type="time" name="availability[${day}][slots][${slotCount}][end]" class="form-control" required>
            <button type="button" class="btn btn-sm btn-outline-danger remove-slot-btn" onclick="removeTimeSlot('${day}', ${slotCount})">
                <i class="ti ti-trash"></i>
            </button>
        </div>
    `;
    
    slotsContainer.insertAdjacentHTML('beforeend', slotHtml);
}

// Function to remove time slot
function removeTimeSlot(day, slotIndex) {
    const slotsContainer = document.getElementById(day + '-slots');
    const slotToRemove = slotsContainer.children[slotIndex];
    if (slotToRemove) {
        slotToRemove.remove();
    }
}

// Make functions global
window.toggleDaySlots = toggleDaySlots;
window.addTimeSlot = addTimeSlot;
window.removeTimeSlot = removeTimeSlot;
//day slots ends


//appoointment starts


// Function to switch appointment views
function switchAppointmentView(viewType) {
    console.log('Switching to appointment view:', viewType);
    
    // Update buttons
    $('#upcoming-btn, #past-btn, #daterange-btn, #canceled-btn').removeClass('btn-primary active').addClass('btn-outline-primary');
    $('#' + viewType + '-btn').removeClass('btn-outline-primary').addClass('btn-primary active');
    
    // Hide all appointment sections
    $('#upcoming-appointments, #past-appointments, #daterange-appointments, #canceled-appointments').hide();
    
    // Show/hide date range filter
    if (viewType === 'daterange') {
        $('#date-range-filter').show();
        $('#daterange-appointments').show();
    } else {
        $('#date-range-filter').hide();
        $('#' + viewType + '-appointments').show();
    }
    
    // Load data based on view type
    loadAppointmentData(viewType);
}

// Function to load appointment data
function loadAppointmentData(viewType) {
    console.log('Loading appointment data for:', viewType);
    
    if (viewType === 'upcoming') {
        loadUpcomingAppointments();
    } else if (viewType === 'past') {
        loadPastAppointments();
    } else if (viewType === 'canceled') {
        loadCanceledAppointments();
    }
}

// Function to load upcoming appointments
function loadUpcomingAppointments() {
    $.ajax({
        url: '{{ route("calendar-events.index") }}',
        method: 'GET',
        success: function(response) {
            const tbody = $('#upcoming-appointments-tbody');
            tbody.empty();
            
            if (response.success && response.data && response.data.length > 0) {
                const now = new Date();
                const upcomingEvents = response.data.filter(event => {
                    // Add null check for event and required properties
                    if (!event || !event.start_date || !event.id) {
                        console.warn('Invalid upcoming event data:', event);
                        return false;
                    }
                    const eventDate = new Date(event.start_date);
                    return eventDate > now; // Only future events
                });

                if (upcomingEvents.length > 0) {
                    upcomingEvents.forEach(function(event) {
                        // Additional safety check
                        if (!event || !event.id) {
                            console.warn('Skipping invalid upcoming event:', event);
                            return;
                        }
                        const startDate = new Date(event.start_date);
                        const formattedDate = startDate.toLocaleDateString() + ' ' + startDate.toLocaleTimeString('en-US', {
                            hour: '2-digit',
                            minute: '2-digit'
                        });
                        
                        const row = `
                            <tr>
                                <td>${event.title}</td>
                                <td>${formattedDate}</td>
                                <td>${event.duration || 60} min</td>
                                <td><span class="badge bg-success">Upcoming</span></td>
                                <td>
                                    <button class="btn btn-sm btn-info me-1" onclick="viewEvent(${event.id})" title="View Details">
                                        <i class="ti ti-eye"></i>
                                    </button>
                                    <button class="btn btn-sm btn-warning me-1" onclick="editEvent(${event.id})" title="Edit">
                                        <i class="ti ti-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger" onclick="deleteEvent(${event.id})" title="Cancel">
                                        <i class="ti ti-x"></i>
                                    </button>
                                </td>
                            </tr>
                        `;
                        tbody.append(row);
                    });
                } else {
                    tbody.html('<tr><td colspan="5" class="text-center text-muted">No upcoming appointments found</td></tr>');
                }
            } else {
                tbody.html('<tr><td colspan="5" class="text-center text-muted">No upcoming appointments found</td></tr>');
            }
        },
        error: function(xhr) {
            console.log('Error loading upcoming appointments:', xhr.responseText);
            $('#upcoming-appointments-tbody').html('<tr><td colspan="5" class="text-center text-danger">Error loading appointments</td></tr>');
        }
    });
}

// Function to load past appointments
function loadPastAppointments() {
    $.ajax({
        url: '{{ route("calendar-events.index") }}',
        method: 'GET',
        success: function(response) {
            const tbody = $('#past-appointments-tbody');
            tbody.empty();
            
            if (response.success && response.data && response.data.length > 0) {
                const now = new Date();
                const pastEvents = response.data.filter(event => {
                    // Add null check for event and required properties
                    if (!event || !event.end_date || !event.id) {
                        console.warn('Invalid past event data:', event);
                        return false;
                    }
                    const eventDate = new Date(event.end_date);
                    return eventDate < now; // Only past events
                });

                if (pastEvents.length > 0) {
                    pastEvents.forEach(function(event) {
                        // Additional safety check
                        if (!event || !event.id) {
                            console.warn('Skipping invalid past event:', event);
                            return;
                        }
                        const startDate = new Date(event.start_date);
                        const formattedDate = startDate.toLocaleDateString() + ' ' + startDate.toLocaleTimeString('en-US', {
                            hour: '2-digit',
                            minute: '2-digit'
                        });
                        
                        const row = `
                            <tr>
                                <td>${event.title}</td>
                                <td>${formattedDate}</td>
                                <td>${event.duration || 60} min</td>
                                <td><span class="badge bg-secondary">Completed</span></td>
                                <td>
                                    <button class="btn btn-sm btn-info" onclick="viewEvent(${event.id})" title="View Details">
                                        <i class="ti ti-eye"></i>
                                    </button>
                                </td>
                            </tr>
                        `;
                        tbody.append(row);
                    });
                } else {
                    tbody.html('<tr><td colspan="5" class="text-center text-muted">No past appointments found</td></tr>');
                }
            } else {
                tbody.html('<tr><td colspan="5" class="text-center text-muted">No past appointments found</td></tr>');
            }
        },
        error: function(xhr) {
            console.log('Error loading past appointments:', xhr.responseText);
            $('#past-appointments-tbody').html('<tr><td colspan="5" class="text-center text-danger">Error loading appointments</td></tr>');
        }
    });
}

// Function to load canceled appointments (placeholder for now)
function loadCanceledAppointments() {
    const tbody = $('#canceled-appointments-tbody');
    tbody.html('<tr><td colspan="5" class="text-center text-muted">No canceled appointments found</td></tr>');
}

// Make functions global
window.switchAppointmentView = switchAppointmentView;

//appointment ends


let calendar;
let editingEventId = null;
let allEvents = [];

// Function to load all events and store them
function loadAllEvents() {
    $.ajax({
        url: '{{ route("calendar-events.index") }}',
        method: 'GET',
        success: function(response) {
            if (response.success && response.data) {
                allEvents = response.data;
                console.log('All events loaded:', allEvents);
                
                // Show today's events by default
                const today = new Date();
                const dayName = today.toLocaleDateString('en-US', { weekday: 'long' });
                updateSelectedDateContainer(today, dayName);
            }
        },
        error: function(xhr) {
            console.log('Error loading events:', xhr.responseText);
        }
    });
}

// Function to update the selected date container
function updateSelectedDateContainer(date, dayName) {
    console.log('Updating sidebar for:', dayName, date);
    
    const options = { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
    };
    
    const formattedDate = date.toLocaleDateString('en-US', options);
    
    $('#selected-date-title').text(dayName);
    $('#selected-date-subtitle').text(formattedDate);
    
    // Show events for selected date
    showEventsForDate(date);
}

// Function to show events for a specific date
function showEventsForDate(selectedDate) {
    console.log('Showing events for:', selectedDate);
    
    const dateStr = selectedDate.toISOString().split('T')[0];
    
    // Filter events for the selected date
    const dayEvents = allEvents.filter(event => {
        const eventDate = new Date(event.start_date).toISOString().split('T')[0];
        return eventDate === dateStr;
    });
    
    console.log('Found events:', dayEvents);
    
    const eventsList = $('#events-list');
    const noEventsMessage = $('#no-events-message');
    
    if (dayEvents.length > 0) {
        noEventsMessage.hide();
        eventsList.show();
        eventsList.empty();
        
        dayEvents.forEach(event => {
            const startTime = new Date(event.start_date).toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit'
            });
            
            const endTime = new Date(event.end_date).toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit'
            });
            
            const eventHtml = `
                <li class="mb-3 p-3 border rounded bg-light">
                    <h6 class="mb-1 text-primary">${event.title}</h6>
                    <div class="small text-muted mb-2">
                        <i class="ti ti-clock me-1"></i>${startTime} - ${endTime}
                    </div>
                    ${event.description ? `<p class="small mb-2">${event.description}</p>` : ''}
                    ${event.location ? `<div class="small text-success"><i class="ti ti-map-pin me-1"></i>${event.location}</div>` : ''}
                </li>
            `;
            eventsList.append(eventHtml);
        });
    } else {
        eventsList.hide();
        noEventsMessage.show();
        noEventsMessage.html(`
            <i class="ti ti-calendar-off fs-1 mb-2 d-block text-muted"></i>
            <div class="text-muted">No events scheduled for this date</div>
        `);
    }
}




// Function to collect availability data
function collectAvailabilityData() {
    const availability = {};

    // Days of the week
    const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];

    days.forEach(function(day) {
        const dayCheckbox = $(`input[name="availability[${day}][enabled]"]`);
        const isEnabled = dayCheckbox.is(':checked');

        availability[day] = {
            enabled: isEnabled,
            slots: []
        };

        if (isEnabled) {
            // Collect time slots for this day
            $(`input[name^="availability[${day}][slots]"][name$="[start]"]`).each(function(index) {
                const startTime = $(this).val();
                const endTime = $(`input[name="availability[${day}][slots][${index}][end]"]`).val();

                if (startTime && endTime) {
                    availability[day].slots.push({
                        start: startTime,
                        end: endTime
                    });
                }
            });
        }
    });

    console.log('Collected availability data:', availability);
    return availability;
}

// Minimum Notice Calculation Functions
function calculateMinimumNoticeInMinutes() {
    const value = parseInt($('#minimum_notice_value').val()) || 0;
    const unit = $('#minimum_notice_unit').val();

    let minutes = 0;
    switch (unit) {
        case 'minutes':
            minutes = value;
            break;
        case 'hours':
            minutes = value * 60;
            break;
        case 'days':
            minutes = value * 60 * 24;
            break;
        case 'months':
            minutes = value * 60 * 24 * 30; // Approximate 30 days per month
            break;
        default:
            minutes = 0;
    }

    // Update the hidden field
    $('#minimum_notice').val(minutes);

    // Update the preview text
    updateMinimumNoticePreview(value, unit);

    return minutes;
}

function setMinimumNoticeFromMinutes(totalMinutes) {
    totalMinutes = parseInt(totalMinutes) || 0;

    if (totalMinutes === 0) {
        $('#minimum_notice_value').val(0);
        $('#minimum_notice_unit').val('minutes');
        updateMinimumNoticePreview(0, 'minutes');
        return;
    }

    // Convert to the most appropriate unit
    if (totalMinutes >= 43200) { // 30 days or more
        const months = Math.floor(totalMinutes / (60 * 24 * 30));
        $('#minimum_notice_value').val(months);
        $('#minimum_notice_unit').val('months');
        updateMinimumNoticePreview(months, 'months');
    } else if (totalMinutes >= 1440) { // 1 day or more
        const days = Math.floor(totalMinutes / (60 * 24));
        $('#minimum_notice_value').val(days);
        $('#minimum_notice_unit').val('days');
        updateMinimumNoticePreview(days, 'days');
    } else if (totalMinutes >= 60) { // 1 hour or more
        const hours = Math.floor(totalMinutes / 60);
        $('#minimum_notice_value').val(hours);
        $('#minimum_notice_unit').val('hours');
        updateMinimumNoticePreview(hours, 'hours');
    } else {
        $('#minimum_notice_value').val(totalMinutes);
        $('#minimum_notice_unit').val('minutes');
        updateMinimumNoticePreview(totalMinutes, 'minutes');
    }
}

function updateMinimumNoticePreview(value, unit) {
    const previewElement = $('#minimum_notice_preview');

    if (value === 0 || !value) {
        previewElement.text('No minimum notice required');
        return;
    }

    let previewText = '';
    if (value === 1) {
        // Singular form
        switch (unit) {
            case 'minutes':
                previewText = '1 minute advance notice required';
                break;
            case 'hours':
                previewText = '1 hour advance notice required';
                break;
            case 'days':
                previewText = '1 day advance notice required';
                break;
            case 'months':
                previewText = '1 month advance notice required';
                break;
        }
    } else {
        // Plural form
        previewText = `${value} ${unit} advance notice required`;
    }

    previewElement.text(previewText);
}

$(document).ready(function() {
    console.log('Document ready - initializing...');

    // Validate that required modal elements exist
    const requiredModalElements = [
        '#viewEventModal', '#view-event-title',
        '#view-event-duration', '#view-event-booking-slots',
        '#view-event-notice'
    ];

    let missingElements = [];
    requiredModalElements.forEach(elementId => {
        if ($(elementId).length === 0) {
            missingElements.push(elementId);
        }
    });

    if (missingElements.length > 0) {
        console.error('Missing required modal elements:', missingElements);
    }

    // Initialize calendar
    initializeCalendar();

    // Load all events for the sidebar
    loadAllEvents();

    // Add event listeners for minimum notice calculation
    $('#minimum_notice_value, #minimum_notice_unit').on('change input', function() {
        calculateMinimumNoticeInMinutes();
    });

    // Initialize minimum notice calculation
    calculateMinimumNoticeInMinutes();
    
    // Form submission handler
    $('#createEventForm').on('submit', function(e) {
        e.preventDefault();
        console.log('Form submitted!'); // Check if this log appears
        console.log('editingEventId at form submission:', editingEventId); // Debug log
        
        const submitBtn = $('#submitBtn');
        const originalText = submitBtn.text();
        
        // Show loading
        submitBtn.prop('disabled', true).text(editingEventId ? 'Updating...' : 'Creating...');
        
        // Collect custom fields data (field type and label only, no values)
        const customFields = [];

        try {
            $('#custom-fields-container .custom-field-row').each(function() {
                const fieldType = $(this).data('field-type');
                const fieldLabel = $(this).data('field-label');
                if (fieldType && fieldLabel) {
                    customFields.push({
                        type: fieldType,
                        label: fieldLabel
                    });
                }
            });
        } catch (error) {
            console.log('Error collecting Additional Info:', error);
        }

        // Get form data
        const formData = {
            title: $('#event_title').val(),
            duration: $('#event_duration').val() || 60,
            booking_per_slot: $('#booking_per_slot').val() || 1,
            minimum_notice: $('#minimum_notice').val() || 0,
            minimum_notice_value: $('#minimum_notice_value').val() || 0,
            minimum_notice_unit: $('#minimum_notice_unit').val() || 'minutes',
            description: $('#event_description').val(),
            locations_data: $('#event_locations_data').val(),
            custom_redirect_url: $('#custom_redirect_url').val(),
            // Legacy fields for backward compatibility
            location: selectedLocations.length > 0 ? selectedLocations[0].type : '',
            meet_link: selectedLocations.find(loc => ['zoom', 'meet', 'skype', 'phone', 'others'].includes(loc.type))?.value || '',
            physical_address: selectedLocations.find(loc => loc.type === 'in_person')?.value || '',
            require_name: $('#require_name').is(':checked'),
            require_email: $('#require_email').is(':checked'),
            require_phone: $('#require_phone').is(':checked'),
            custom_field: $('#custom_field').val(), // Single field for backward compatibility
            custom_fields: customFields, // Multiple field names array (no values)
            availability: (function() {
                try {
                    return collectAvailabilityData();
                } catch (error) {
                    console.log('Error collecting availability data:', error);
                    return {};
                }
            })(),
            date_override: (function() {
                try {
                    return collectDateOverrideData();
                } catch (error) {
                    console.log('Error collecting date override data:', error);
                    return {};
                }
            })(),
            _token: $('input[name="_token"]').val()
        };

        // Add _method field for PUT request when updating
        if (editingEventId) {
            formData._method = 'PUT';
        }
        
        console.log('Sending data:', formData); // Log form data

        // Determine URL - always use POST but add _method for PUT
        const url = editingEventId ?
            `{{ url('calendar-events') }}/${editingEventId}` :
            '{{ route("calendar-events.store") }}';

        console.log('AJAX URL:', url); // Log the URL
        console.log('Editing Event ID:', editingEventId); // Log editing state
        
        // Send AJAX request
        $.ajax({
            url: url,
            method: 'POST',
            data: formData,
            success: function(response) {
                console.log('Success response:', response);
                
                // Reset button
                submitBtn.prop('disabled', false).text(originalText);
                
                if (response.success) {
                    // Close modal
                    $('#createEventModal').modal('hide');
                    
                    // Reset form
                    resetForm();
                    
                    // Show success toast message
                    console.log('editingEventId at success:', editingEventId); // Debug log
                    show_toastr('success', editingEventId ? 'Event updated successfully!' : 'Event created successfully!');
                    
                    // Reset editing state
                    editingEventId = null;
                    
                    // Refresh calendar and events
                    if (calendar) {
                        calendar.refetchEvents();
                    }
                    loadEventsList();
                    loadAllEvents(); // Refresh sidebar events
                } else {
                    console.log('Validation errors:', response.errors); // Log validation errors
                    show_toastr('error', response.message || 'Unknown error');
                }
            },
            error: function(xhr, status, error) {
                console.log('Error response:', xhr.responseText);
                
                // Reset button
                submitBtn.prop('disabled', false).text(originalText);
                
                let errorMessage = 'An error occurred';
                
                try {
                    const errorResponse = JSON.parse(xhr.responseText);
                    if (errorResponse.errors) {
                        errorMessage = 'Validation errors:\n';
                        Object.keys(errorResponse.errors).forEach(key => {
                            errorMessage += `${key}: ${errorResponse.errors[key].join(', ')}\n`;
                        });
                    } else if (errorResponse.message) {
                        errorMessage = errorResponse.message;
                    }
                } catch (e) {
                    errorMessage = `Server Error (${xhr.status}): ${xhr.responseText || 'Unknown error'}`;
                }
                
                show_toastr('error', errorMessage);
            }
        });
    });
});
   

// Initialize calendar
function initializeCalendar() {
    console.log('Initializing calendar...');
    
    if (typeof FullCalendar === 'undefined') {
        console.error('FullCalendar not loaded');
        return;
    }
    
    const calendarEl = document.getElementById('calendar');
    if (!calendarEl) {
        console.error('Calendar element not found');
        return;
    }
    
    try {
        calendar = new FullCalendar.Calendar(calendarEl, {
            initialView: 'dayGridMonth',
            headerToolbar: {
                left: 'prev,next today',
                center: 'title',
                right: 'dayGridMonth,timeGridWeek,timeGridDay'
            },
            buttonText: {
                today: "{{ __('Today') }}",
                month: "{{ __('Month') }}",
                week: "{{ __('Week') }}",
                day: "{{ __('Day') }}"
            },
            height: 'auto',
            selectable: false,
            selectMirror: false,
            events: function(fetchInfo, successCallback, failureCallback) {
                $.ajax({
                    url: "{{ route('calendar-events.calendar-data') }}",
                    method: "POST",
                    data: {
                        "_token": "{{ csrf_token() }}"
                    },
                    success: function(data) {
                        console.log('Calendar data loaded:', data);
                        successCallback(data);
                    },
                    error: function(xhr, status, error) {
                        console.log('Error loading calendar data:', error);
                        failureCallback(error);
                    }
                });
            },
            // select: function(info) {
            //     // This function is disabled to prevent interference with dateClick
            //     // If you need to create events, use the "Create Event" button instead
            // },
            eventClick: function(info) {
                // Add these lines:
                if (info.jsEvent) {
                    info.jsEvent.stopPropagation();
                    info.jsEvent.preventDefault();
                }
                // ...existing code...
                if (!info || !info.event) {
                    console.warn('Invalid event click info:', info);
                    return;
                }

                const eventId = info.event.id;
                console.log('Calendar event clicked, raw ID:', eventId);

                if (eventId) {
                    // Check if this is an appointment booking (different handling)
                    if (typeof eventId === 'string' && eventId.startsWith('appointment_')) {
                        console.log('Appointment booking clicked, not handling in viewEvent');
                        return;
                    }

                    // Extract numeric ID from prefixed ID (e.g., 'event_123' -> '123')
                    let numericId = eventId;
                    if (typeof eventId === 'string' && eventId.startsWith('event_')) {
                        numericId = eventId.replace('event_', '');
                        console.log('Extracted numeric ID from prefixed calendar event:', numericId);
                    }

                    // Convert to integer to ensure consistency
                    const finalId = parseInt(numericId);
                    if (isNaN(finalId)) {
                        console.error('Could not extract valid numeric ID from:', eventId);
                        return;
                    }

                    console.log('Calling viewEvent with numeric ID:', finalId);
                    viewEvent(finalId);
                }
            },
            dateClick: function(info) {
                console.log('Date clicked:', info);

                const clickedDate = new Date(info.date);
                const formattedDate = clickedDate.toISOString().split('T')[0];

                // Set the selected date in the appointment form
                // Load available events
                loadAvailableEvents();

                // Show the appointment booking modal
                $('#appointmentBookingModal').modal('show');
            }
        });
        
        calendar.render();
        console.log('Calendar initialized successfully');
        
    } catch (error) {
        console.error('Error initializing calendar:', error);
    }
}


//custom field
let customFieldCounter = 0;

function toggleCustomField() {
    // This function is now just for compatibility
    // The actual functionality is handled by addCustomField()
}

function addCustomField() {
    const selectedOption = $('#custom_field option:selected');
    const fieldValue = selectedOption.val();
    const fieldLabel = selectedOption.text();

    // Don't add if no field is selected
    if (!fieldValue) {
        alert('Please select a field type first');
        return;
    }

    // Check if this field already exists
    if ($(`#custom-field-${fieldValue}`).length > 0) {
        alert('This field has already been added');
        return;
    }

    customFieldCounter++;
    const fieldId = `custom-field-${fieldValue}`;

    let inputHtml = '';

    // Generate appropriate input based on field type (DISABLED for event creation)
    if (fieldValue === 'date_of_birth') {
        inputHtml = `<input type="date" class="form-control" id="${fieldId}" name="custom_fields[${fieldValue}]" placeholder="Select date" disabled>`;
    } else if (fieldValue === 'contact_type') {
        inputHtml = `
            <select class="form-control" id="${fieldId}" name="custom_fields[${fieldValue}]" disabled>
                <option value="">Select Contact Type</option>
                <option value="phone">Phone</option>
                <option value="email">Email</option>
                <option value="in_person">In Person</option>
                <option value="other">Other</option>
            </select>
        `;
    } else if (fieldValue === 'have_msme_certificate' || fieldValue === 'have_website' || fieldValue === 'used_whatsapp_api_chatbots' || fieldValue === 'know_whatsapp_api') {
        inputHtml = `
            <select class="form-control" id="${fieldId}" name="custom_fields[${fieldValue}]" disabled>
                <option value="">Select Option</option>
                <option value="yes">Yes</option>
                <option value="no">No</option>
            </select>
        `;
    } else if (fieldValue === 'team_size') {
        inputHtml = `
            <select class="form-control" id="${fieldId}" name="custom_fields[${fieldValue}]" disabled>
                <option value="">Select Team Size</option>
                <option value="1-5">1-5 employees</option>
                <option value="6-10">6-10 employees</option>
                <option value="11-25">11-25 employees</option>
                <option value="26-50">26-50 employees</option>
                <option value="51-100">51-100 employees</option>
                <option value="100+">100+ employees</option>
            </select>
        `;
    } else if (fieldValue === 'lead_value' || fieldValue === 'budget' || fieldValue === 'company_revenue' || fieldValue === 'monthly_advertising_budget' || fieldValue === 'property_price_range') {
        inputHtml = `<input type="number" class="form-control" id="${fieldId}" name="custom_fields[${fieldValue}]" placeholder="Enter amount" disabled>`;
    } else if (fieldValue === 'specific_requirement' || fieldValue === 'message' || fieldValue === 'biggest_marketing_challenge') {
        inputHtml = `<textarea class="form-control" id="${fieldId}" name="custom_fields[${fieldValue}]" rows="3" placeholder="Enter details" disabled></textarea>`;
    } else {
        inputHtml = `<input type="text" class="form-control" id="${fieldId}" name="custom_fields[${fieldValue}]" placeholder="Enter ${fieldLabel.toLowerCase()}" disabled>`;
    }

    // Create the field HTML
    const fieldHtml = `
        <div class="row mb-3 custom-field-row" data-field="${fieldValue}" data-field-type="${fieldValue}" data-field-label="${fieldLabel}">
            <div class="col-10">
                <label for="${fieldId}" class="form-label">${fieldLabel}</label>
                ${inputHtml}
            </div>
            <div class="col-2 d-flex align-items-end">
                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeCustomField('${fieldValue}')">
                    <i class="ti ti-trash"></i>
                </button>
            </div>
        </div>
    `;

    // Add the field to the container
    $('#custom-fields-container').append(fieldHtml);

    // Reset the dropdown
    $('#custom_field').val('');
}

function removeCustomField(fieldValue) {
    $(`.custom-field-row[data-field="${fieldValue}"]`).remove();
}

// Function to populate custom fields when editing an event
function populateCustomFieldsForEdit(event) {
    // Clear existing custom fields
    $('#custom-fields-container').empty();
    customFieldCounter = 0;

    // Check if event has custom fields
    if (event.custom_fields && Array.isArray(event.custom_fields)) {
        event.custom_fields.forEach(function(field) {
            if (field.type && field.label) {
                // Add the custom field to the form
                addCustomFieldForEdit(field.type, field.label);
            }
        });
    }
}

// Function to add a custom field during edit (similar to addCustomField but for editing)
function addCustomFieldForEdit(fieldType, fieldLabel) {
    // Check if this field already exists
    if ($(`#custom-field-${fieldType}`).length > 0) {
        return; // Field already exists
    }

    customFieldCounter++;
    const fieldId = `custom-field-${fieldType}`;

    let inputHtml = '';

    // Generate appropriate input based on field type (DISABLED for event creation)
    if (fieldType === 'date_of_birth') {
        inputHtml = `<input type="date" class="form-control" id="${fieldId}" name="custom_fields[${fieldType}]" placeholder="Select date" disabled>`;
    } else if (fieldType === 'contact_type') {
        inputHtml = `
            <select class="form-control" id="${fieldId}" name="custom_fields[${fieldType}]" disabled>
                <option value="">Select Contact Type</option>
                <option value="phone">Phone</option>
                <option value="email">Email</option>
                <option value="in_person">In Person</option>
                <option value="other">Other</option>
            </select>
        `;
    } else if (fieldType === 'have_msme_certificate' || fieldType === 'have_website' || fieldType === 'used_whatsapp_api_chatbots' || fieldType === 'know_whatsapp_api') {
        inputHtml = `
            <select class="form-control" id="${fieldId}" name="custom_fields[${fieldType}]" disabled>
                <option value="">Select Option</option>
                <option value="yes">Yes</option>
                <option value="no">No</option>
            </select>
        `;
    } else if (fieldType === 'team_size') {
        inputHtml = `
            <select class="form-control" id="${fieldId}" name="custom_fields[${fieldType}]" disabled>
                <option value="">Select Team Size</option>
                <option value="1-5">1-5 employees</option>
                <option value="6-10">6-10 employees</option>
                <option value="11-25">11-25 employees</option>
                <option value="26-50">26-50 employees</option>
                <option value="51-100">51-100 employees</option>
                <option value="100+">100+ employees</option>
            </select>
        `;
    } else if (fieldType === 'lead_value' || fieldType === 'budget' || fieldType === 'company_revenue' || fieldType === 'monthly_advertising_budget' || fieldType === 'property_price_range') {
        inputHtml = `<input type="number" class="form-control" id="${fieldId}" name="custom_fields[${fieldType}]" placeholder="Enter amount" disabled>`;
    } else if (fieldType === 'specific_requirement' || fieldType === 'message' || fieldType === 'biggest_marketing_challenge') {
        inputHtml = `<textarea class="form-control" id="${fieldId}" name="custom_fields[${fieldType}]" rows="3" placeholder="Enter details" disabled></textarea>`;
    } else {
        inputHtml = `<input type="text" class="form-control" id="${fieldId}" name="custom_fields[${fieldType}]" placeholder="Enter ${fieldLabel.toLowerCase()}" disabled>`;
    }

    // Create the field HTML
    const fieldHtml = `
        <div class="row mb-3 custom-field-row" data-field="${fieldType}" data-field-type="${fieldType}" data-field-label="${fieldLabel}">
            <div class="col-10">
                <label for="${fieldId}" class="form-label">${fieldLabel}</label>
                ${inputHtml}
            </div>
            <div class="col-2 d-flex align-items-end">
                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeCustomField('${fieldType}')">
                    <i class="ti ti-trash"></i>
                </button>
            </div>
        </div>
    `;

    // Add the field to the container
    $('#custom-fields-container').append(fieldHtml);
}

// Appointment Booking Functions
function loadAvailableEvents() {
    console.log('Loading available events');

    // Clear previous options
    $('#appointment_calendar_event').html('<option value="">{{ __("Select an event") }}</option>');
    $('#appointment_timeslot').html('<option value="">{{ __("Select a time slot") }}</option>');
    $('#appointment_location_type').val('');
    $('#appointment_location_value').val('');

    // Make AJAX call to get all available events
    $.ajax({
        url: '{{ route("calendar-events.index") }}',
        method: 'GET',
        success: function(response) {
            if (response.success && response.data.length > 0) {
                // Store events globally for time slot generation
                window.availableEvents = response.data;

                response.data.forEach(function(event) {
                    // Add null check for event and required properties
                    if (!event || !event.id || !event.title) {
                        console.warn('Skipping invalid event in appointment dropdown:', event);
                        return;
                    }

                    // Determine location value based on location type
                    let locationValue = '';
                    if (event.location === 'in_person') {
                        locationValue = event.physical_address || 'Physical address not specified';
                    } else {
                        locationValue = event.meet_link || 'Meeting link not specified';
                    }

                    $('#appointment_calendar_event').append(
                        `<option value="${event.id}" data-location="${event.location || ''}" data-location-value="${locationValue}" data-duration="${event.duration || 60}">
                            ${event.title} (${event.duration || 60} min)
                        </option>`
                    );
                });
            } else {
                window.availableEvents = [];
                $('#appointment_calendar_event').append('<option value="" disabled>{{ __("No events available for this date") }}</option>');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error loading events:', error);
            window.availableEvents = [];
            alert('Error loading events for the selected date');
        }
    });
}

function updateEventDetails() {
    const selectedOption = $('#appointment_calendar_event option:selected');
    const eventId = selectedOption.val();

    if (eventId) {
        // Get event data from the global events array
        const selectedEvent = window.availableEvents?.find(event => event.id == eventId);

        if (selectedEvent) {
            // Handle new location system
            let locationDisplay = '';
            let locationValue = '';

            if (selectedEvent.locations_data) {
                // New multi-location system
                try {
                    const locations = JSON.parse(selectedEvent.locations_data);
                    if (locations && locations.length > 0) {
                        // Display all locations
                        locationDisplay = locations.map(loc => loc.display).join(', ');
                        // Use the first location's value for the appointment
                        locationValue = locations[0].value || 'Not specified';
                    }
                } catch (e) {
                    console.error('Error parsing locations_data:', e);
                    locationDisplay = 'Location data error';
                    locationValue = 'Not specified';
                }
            } else {
                // Legacy single location system
                const location = selectedEvent.location;
                locationDisplay = getLocationDisplayName(location);

                if (location === 'in_person') {
                    locationValue = selectedEvent.physical_address || 'Physical address not specified';
                } else {
                    locationValue = selectedEvent.meet_link || 'Meeting link not specified';
                }
            }

            // Update location fields
            $('#appointment_location_type').val(locationDisplay);
            $('#appointment_location_value').val(locationValue);

            // Set the event date from the calendar event's start date
            if (selectedEvent.start_date) {
                const eventDate = new Date(selectedEvent.start_date);
                const formattedDate = eventDate.toISOString().split('T')[0]; // YYYY-MM-DD format
                $('#appointment_date').val(formattedDate);
            }
        } else {
            // Fallback to data attributes if global events not available
            const location = selectedOption.data('location');
            const locationValue = selectedOption.data('location-value');

            $('#appointment_location_type').val(getLocationDisplayName(location));
            $('#appointment_location_value').val(locationValue || 'Not specified');

            // Set current date as fallback
            $('#appointment_date').val(new Date().toISOString().split('T')[0]);
        }

        // Generate time slots for the selected event
        generateSimpleTimeSlots();
    } else {
        // Clear fields if no event selected
        $('#appointment_location_type').val('');
        $('#appointment_location_value').val('');
        $('#appointment_date').val('');
        $('#appointment_timeslot').html('<option value="">{{ __("Select a time slot") }}</option>');
    }
}

function getLocationDisplayName(location) {
    const locationNames = {
        'in_person': 'In Person',
        'zoom': 'Zoom',
        'skype': 'Skype',
        'meet': 'Google Meet',
        'phone': 'Phone Call',
        'others': 'Other'
    };
    return locationNames[location] || location;
}

function generateTimeSlots(eventId, duration) {
    console.log('Generating time slots for event:', eventId);

    // Generate time slots based on booking table data
    generateTimeSlotsFromBookingData(eventId);
}

function generateSimpleTimeSlots() {
    console.log('Generating simple time slots');
    $('#appointment_timeslot').html('<option value="">{{ __("Select a time slot") }}</option>');

    // Start from 10:00 AM and go to 5:00 PM in 30-minute intervals
    let currentHour = 10;
    let currentMinute = 0;
    const endHour = 17;

    while (currentHour < endHour || (currentHour === endHour && currentMinute === 0)) {
        const timeString = `${currentHour.toString().padStart(2, '0')}:${currentMinute.toString().padStart(2, '0')}`;
        const displayTime = formatTime12Hour(timeString);

        console.log('Adding simple time slot:', timeString, '->', displayTime);
        $('#appointment_timeslot').append(`<option value="${timeString}">${displayTime}</option>`);

        // Add 30 minutes
        currentMinute += 30;
        if (currentMinute >= 60) {
            currentMinute = 0;
            currentHour += 1;
        }
    }

    console.log('Simple time slots generation completed');
}

function generateTimeSlotsFromBookingData(eventId) {
    console.log('Getting booking data for event:', eventId);

    // Get booking time from the booking table for this event
    $.ajax({
        url: '{{ route("calendar-events.index") }}',
        method: 'GET',
        success: function(response) {
            console.log('Events response:', response);
            $('#appointment_timeslot').html('<option value="">{{ __("Select a time slot") }}</option>');

            if (response.success && response.data) {
                // Find the selected event and get its booking time
                const selectedEvent = response.data.find(event => event.id == eventId);
                console.log('Selected event:', selectedEvent);

                let bookingStartTime = '10:00'; // Default start time

                if (selectedEvent && selectedEvent.bookings && selectedEvent.bookings.length > 0) {
                    // Get the time from the first booking for this event
                    bookingStartTime = selectedEvent.bookings[0].time;
                    console.log('Found booking start time:', bookingStartTime);
                } else {
                    console.log('No bookings found, using default time:', bookingStartTime);
                }

                // Parse start time from booking
                const [startHour, startMinute] = bookingStartTime.split(':').map(Number);
                console.log('Start hour:', startHour, 'Start minute:', startMinute);

                // End time is 5:00 PM (17:00)
                const endHour = 17;
                const endMinute = 0;

                // Generate time slots in 30-minute intervals
                let currentHour = startHour;
                let currentMinute = startMinute;

                console.log('Generating time slots from', currentHour + ':' + currentMinute, 'to 17:00');

                while (currentHour < endHour || (currentHour === endHour && currentMinute <= endMinute)) {
                    const timeString = `${currentHour.toString().padStart(2, '0')}:${currentMinute.toString().padStart(2, '0')}`;
                    const displayTime = formatTime12Hour(timeString);

                    console.log('Adding time slot:', timeString, '->', displayTime);
                    $('#appointment_timeslot').append(`<option value="${timeString}">${displayTime}</option>`);

                    // Add 30 minutes
                    currentMinute += 30;
                    if (currentMinute >= 60) {
                        currentMinute = 0;
                        currentHour += 1;
                    }

                    // Stop if we've reached past 5:00 PM
                    if (currentHour > endHour || (currentHour === endHour && currentMinute > endMinute)) {
                        break;
                    }
                }

                console.log('Time slots generation completed');
            } else {
                console.log('No event data found, using fallback');
                // Final fallback
                generateFallbackTimeSlots();
            }
        },
        error: function(xhr, status, error) {
            console.error('Error loading booking data:', error);
            // Final fallback
            generateFallbackTimeSlots();
        }
    });
}

function generateDefaultTimeSlots(duration = 60) {
    $('#appointment_timeslot').html('<option value="">{{ __("Select a time slot") }}</option>');

    const selectedEventId = $('#appointment_calendar_event').val();

    if (!selectedEventId) {
        return;
    }

    // Get booking time from the booking table for this event
    $.ajax({
        url: '{{ route("calendar-events.index") }}',
        method: 'GET',
        success: function(response) {
            if (response.success && response.data) {
                // Find the selected event and get its booking time
                const selectedEvent = response.data.find(event => event.id == selectedEventId);
                let bookingStartTime = '09:00'; // Default start time

                if (selectedEvent && selectedEvent.bookings && selectedEvent.bookings.length > 0) {
                    // Get the time from the first booking (or you can modify this logic)
                    bookingStartTime = selectedEvent.bookings[0].time;
                }

                // Parse start time from booking
                const [startHour, startMinute] = bookingStartTime.split(':').map(Number);

                // End time is 5:00 PM (17:00)
                const endHour = 17;
                const endMinute = 0;

                // Generate time slots in 30-minute intervals
                let currentHour = startHour;
                let currentMinute = startMinute;

                while (currentHour < endHour || (currentHour === endHour && currentMinute <= endMinute)) {
                    const timeString = `${currentHour.toString().padStart(2, '0')}:${currentMinute.toString().padStart(2, '0')}`;
                    const displayTime = formatTime12Hour(timeString);

                    $('#appointment_timeslot').append(`<option value="${timeString}">${displayTime}</option>`);

                    // Add 30 minutes
                    currentMinute += 30;
                    if (currentMinute >= 60) {
                        currentMinute = 0;
                        currentHour += 1;
                    }

                    // Stop if we've reached past 5:00 PM
                    if (currentHour > endHour || (currentHour === endHour && currentMinute > endMinute)) {
                        break;
                    }
                }
            }
        },
        error: function(xhr, status, error) {
            console.error('Error loading booking times:', error);
            // Fallback to default 9 AM to 5 PM slots
            generateFallbackTimeSlots();
        }
    });
}

function generateFallbackTimeSlots() {
    console.log('Generating fallback time slots');
    $('#appointment_timeslot').html('<option value="">{{ __("Select a time slot") }}</option>');

    // Generate default time slots from 10 AM to 5 PM in 30-minute intervals
    let currentHour = 10;
    let currentMinute = 0;
    const endHour = 17;
    const endMinute = 0;

    console.log('Fallback: generating from 10:00 to 17:00');

    while (currentHour < endHour || (currentHour === endHour && currentMinute <= endMinute)) {
        const timeString = `${currentHour.toString().padStart(2, '0')}:${currentMinute.toString().padStart(2, '0')}`;
        const displayTime = formatTime12Hour(timeString);

        console.log('Fallback: adding time slot:', timeString, '->', displayTime);
        $('#appointment_timeslot').append(`<option value="${timeString}">${displayTime}</option>`);

        // Add 30 minutes
        currentMinute += 30;
        if (currentMinute >= 60) {
            currentMinute = 0;
            currentHour += 1;
        }

        // Stop if we've reached past 5:00 PM
        if (currentHour > endHour || (currentHour === endHour && currentMinute > endMinute)) {
            break;
        }
    }

    console.log('Fallback time slots generation completed');
}

function formatTime12Hour(time24) {
    const [hours, minutes] = time24.split(':');
    const hour12 = hours % 12 || 12;
    const ampm = hours < 12 ? 'AM' : 'PM';
    return `${hour12}:${minutes} ${ampm}`;
}

// Populate timezone dropdown with GMT as default
function populateTimezones() {
    const timezoneSelect = $('#appointment_timezone');

    // Clear existing options except the first one
    timezoneSelect.find('option:not(:first)').remove();

    // Add GMT as the primary option (selected by default)
    timezoneSelect.append(`<option value="GMT" selected>GMT (Greenwich Mean Time) (UTC+00:00)</option>`);

    // Add separator
    timezoneSelect.append('<option disabled>──────────────────────</option>');

    // Add common timezones
    const commonTimezones = [
        { value: 'UTC', display: 'UTC (Coordinated Universal Time) (UTC+00:00)' },
        { value: 'Europe/London', display: 'London (UTC+00:00/+01:00)' },
        { value: 'America/New_York', display: 'New York (UTC-05:00/-04:00)' },
        { value: 'America/Los_Angeles', display: 'Los Angeles (UTC-08:00/-07:00)' },
        { value: 'Europe/Paris', display: 'Paris (UTC+01:00/+02:00)' },
        { value: 'Asia/Tokyo', display: 'Tokyo (UTC+09:00)' },
        { value: 'Asia/Dubai', display: 'Dubai (UTC+04:00)' },
        { value: 'Asia/Kolkata', display: 'India (UTC+05:30)' },
        { value: 'Australia/Sydney', display: 'Sydney (UTC+10:00/+11:00)' }
    ];

    // Add common timezones group
    timezoneSelect.append('<optgroup label="Common Timezones">');
    commonTimezones.forEach(tz => {
        timezoneSelect.append(`<option value="${tz.value}">${tz.display}</option>`);
    });
    timezoneSelect.append('</optgroup>');

    // Add all other timezones
    const timezones = Intl.supportedValuesOf('timeZone');
    const groupedTimezones = {};

    timezones.forEach(timezone => {
        // Skip if already in common timezones
        if (commonTimezones.some(ct => ct.value === timezone) || timezone === 'GMT') return;

        const parts = timezone.split('/');
        const region = parts[0];
        const city = parts.slice(1).join('/');

        if (!groupedTimezones[region]) {
            groupedTimezones[region] = [];
        }

        const offset = getTimezoneOffset(timezone);
        // Format timezone display name
        let displayName = city.replace(/_/g, ' ');

        // Special formatting for common locations
        if (timezone === 'Asia/Kolkata') {
            displayName = 'India';
        } else if (timezone === 'Asia/Shanghai') {
            displayName = 'China';
        } else if (timezone === 'Europe/London') {
            displayName = 'United Kingdom';
        }

        groupedTimezones[region].push({
            value: timezone,
            display: `${displayName} ${offset}`,
            offset: offset
        });
    });

    // Sort regions and add to select
    Object.keys(groupedTimezones).sort().forEach(region => {
        // Sort cities within region by offset
        groupedTimezones[region].sort((a, b) => {
            const offsetA = parseFloat(a.offset.replace(/[^\d.-]/g, ''));
            const offsetB = parseFloat(b.offset.replace(/[^\d.-]/g, ''));
            return offsetA - offsetB;
        });

        // Add region header
        timezoneSelect.append(`<optgroup label="${region}">`);

        // Add cities in this region
        groupedTimezones[region].forEach(tz => {
            timezoneSelect.append(`<option value="${tz.value}">${tz.display}</option>`);
        });

        timezoneSelect.append('</optgroup>');
    });
}

// Helper function to get timezone offset
function getTimezoneOffset(timezone) {
    const now = new Date();
    const utc = new Date(now.getTime() + (now.getTimezoneOffset() * 60000));
    const targetTime = new Date(utc.toLocaleString("en-US", {timeZone: timezone}));
    const diff = targetTime.getTime() - utc.getTime();
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    const sign = hours >= 0 ? '+' : '-';
    const absHours = Math.abs(hours);
    const absMinutes = Math.abs(minutes);

    return `(UTC${sign}${absHours.toString().padStart(2, '0')}:${absMinutes.toString().padStart(2, '0')})`;
}

// Handle appointment booking form submission
$(document).ready(function() {
    // Populate timezones when document is ready
    populateTimezones();

    $('#appointmentBookingForm').on('submit', function(e) {
        e.preventDefault();

        const formData = {
            event_id: $('#appointment_calendar_event').val(),
            event_location: $('#appointment_location_type').val(),
            event_location_value: $('#appointment_location_value').val(),
            event_date: $('#appointment_date').val(), // Event's start date
            time_zone: $('#appointment_timezone').val(),
            time_slots: $('#appointment_timeslot').val(),
            _token: $('input[name="_token"]').val()
        };

        // Validate required fields
        if (!formData.event_id || !formData.event_location || !formData.event_date || !formData.time_slots || !formData.time_zone) {
            alert('Please fill in all required fields');
            return;
        }

        // Disable submit button
        $('#appointmentSubmitBtn').prop('disabled', true).html('<i class="ti ti-loader me-1"></i>{{ __("Booking...") }}');

        // Submit appointment booking
        $.ajax({
            url: '{{ route("appointment-bookings.store") }}',
            method: 'POST',
            data: formData,
            success: function(response) {
                if (response.success) {
                    show_toastr('success', 'Appointment booked successfully!');
                    $('#appointmentBookingModal').modal('hide');
                    resetAppointmentForm();

                    // Refresh calendar to show the new appointment
                    if (typeof calendar !== 'undefined') {
                        calendar.refetchEvents();
                    }
                } else {
                    show_toastr('error', response.message || 'Failed to book appointment');
                }
            },
            error: function(xhr, status, error) {
                console.error('Error booking appointment:', error);
                let errorMessage = 'Error booking appointment';

                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                    const errors = Object.values(xhr.responseJSON.errors).flat();
                    errorMessage = errors.join(', ');
                }

                show_toastr('error', errorMessage);
            },
            complete: function() {
                // Re-enable submit button
                $('#appointmentSubmitBtn').prop('disabled', false).html('<i class="ti ti-check me-1"></i>{{ __("Book Appointment") }}');
            }
        });
    });
});

function resetAppointmentForm() {
    $('#appointmentBookingForm')[0].reset();
    $('#appointment_calendar_event').html('<option value="">{{ __("Select an event") }}</option>');
    $('#appointment_timeslot').html('<option value="">{{ __("Select a time slot") }}</option>');
    $('#appointment_location_type').val('');
    $('#appointment_location_value').val('');
    $('#appointment_date').val('');
    // Set timezone to GMT by default
    $('#appointment_timezone').val('GMT');
}



    // Update your editEvent function to populate custom fields if they exist:
    // (This function is removed to avoid duplication - see the main editEvent function below)


// Add this to your global functions at the bottom:
window.toggleCustomField = toggleCustomField;//custom field

// Global variable to store selected locations
let selectedLocations = [];

// Open location modal
function openLocationModal(locationType) {
    const modalId = locationType + 'LocationModal';
    const modal = new bootstrap.Modal(document.getElementById(modalId));
    modal.show();
}

// Save location from modal
function saveLocation(locationType) {
    let locationData = {
        type: locationType,
        value: '',
        display: ''
    };

    // Get the value based on location type
    switch(locationType) {
        case 'zoom':
            locationData.value = $('#zoom_link').val();
            locationData.display = 'Zoom';
            break;
        case 'in_person':
            locationData.value = $('#in_person_address').val();
            locationData.display = 'In-person meeting';
            break;
        case 'phone':
            locationData.value = $('#phone_number').val();
            locationData.display = 'Phone call';
            break;
        case 'meet':
            locationData.value = $('#meet_link').val();
            locationData.display = 'Google Meet';
            break;
        case 'skype':
            locationData.value = $('#skype_link').val();
            locationData.display = 'Skype';
            break;
        case 'others':
            locationData.value = $('#others_details').val();
            locationData.display = 'Others';
            break;
    }

    // Validate that value is provided
    if (!locationData.value.trim()) {
        alert('Please enter the location details');
        return;
    }

    // Check if this location type already exists
    const existingIndex = selectedLocations.findIndex(loc => loc.type === locationType);
    if (existingIndex !== -1) {
        // Update existing location
        selectedLocations[existingIndex] = locationData;
    } else {
        // Add new location
        selectedLocations.push(locationData);
    }

    // Update the display
    updateSelectedLocationsDisplay();

    // Close the modal
    const modalId = locationType + 'LocationModal';
    const modal = bootstrap.Modal.getInstance(document.getElementById(modalId));
    modal.hide();

    // Clear the form
    clearLocationModal(locationType);
}

// Clear location modal form
function clearLocationModal(locationType) {
    switch(locationType) {
        case 'zoom':
            $('#zoom_link').val('');
            break;
        case 'in_person':
            $('#in_person_address').val('');
            break;
        case 'phone':
            $('#phone_number').val('');
            break;
        case 'meet':
            $('#meet_link').val('');
            break;
        case 'skype':
            $('#skype_link').val('');
            break;
        case 'others':
            $('#others_details').val('');
            break;
    }
}

// Remove location
function removeLocation(locationType) {
    selectedLocations = selectedLocations.filter(loc => loc.type !== locationType);
    updateSelectedLocationsDisplay();
}

// Update the display of selected locations
function updateSelectedLocationsDisplay() {
    const container = $('#selected-locations-container');
    container.empty();

    if (selectedLocations.length === 0) {
        container.hide();
        $('#event_locations_data').val('');
        return;
    }

    container.show();

    selectedLocations.forEach(location => {
        const chip = createLocationChip(location);
        container.append(chip);
    });

    // Update hidden input with location data
    $('#event_locations_data').val(JSON.stringify(selectedLocations));
}

// Create location chip element
function createLocationChip(location) {
    const iconMap = {
        'zoom': 'ti-video text-primary',
        'in_person': 'ti-map-pin text-success',
        'phone': 'ti-phone text-info',
        'meet': 'ti-brand-google text-warning',
        'skype': 'ti-brand-skype text-primary',
        'others': 'ti-dots text-secondary'
    };

    const icon = iconMap[location.type] || 'ti-map-pin';

    return $(`
        <div class="d-inline-flex align-items-center bg-light border rounded-pill px-3 py-2 me-2 mb-2">
            <i class="ti ${icon} me-2"></i>
            <span class="fw-medium">${location.display}</span>
            <button type="button" class="btn btn-sm btn-link text-danger p-0 ms-2" onclick="removeLocation('${location.type}')" title="Remove">
                <i class="ti ti-x"></i>
            </button>
        </div>
    `);
}

// Legacy function for backward compatibility
function toggleLocationFields() {
    // This function is kept for backward compatibility but the new system doesn't use it
    console.log('toggleLocationFields called - using new location system');
}

// Reset form to initial state
function resetForm() {
    $('#createEventForm')[0].reset();

    // Clear selected locations
    selectedLocations = [];
    updateSelectedLocationsDisplay();

    // Clear all location modal forms
    clearLocationModal('zoom');
    clearLocationModal('in_person');
    clearLocationModal('phone');
    clearLocationModal('meet');
    clearLocationModal('skype');
    clearLocationModal('others');

    // Clear custom fields container
    $('#custom-fields-container').empty();
    customFieldCounter = 0;

    // Reset minimum notice to default values
    $('#minimum_notice_value').val(0);
    $('#minimum_notice_unit').val('minutes');
    $('#minimum_notice').val(0);
    updateMinimumNoticePreview(0, 'minutes');

    // Reset modal title and button text
    $('.modal-title').text('{{ __("Create New Event") }}');
    $('#submitBtn').text('{{ __("Create Event") }}');

    // Reset editing state
    editingEventId = null;

    console.log('Form reset completed');
}

// Open create event modal
function openCreateEventModal() {
    console.log('Opening create modal...');
    resetForm();
    $('#createEventModal').modal('show');
}

// Edit event function
function editEvent(eventId) {
    console.log('Editing event:', eventId);
    
    // Set editing state
    editingEventId = eventId;
    
    // Update modal title and button text
    $('.modal-title').text('{{ __("Event Details") }}');
    $('#submitBtn').text('{{ __("Update Event") }}');
    
    // Load event data
    const url = `{{ url('calendar-events') }}/${eventId}/edit-data`;
    console.log('Making AJAX request to:', url);

    $.ajax({
        url: url,
        method: 'GET',
        success: function(response) {
            console.log('Event data loaded:', response);
            
            if (response.success) {
                const event = response.data;
                
                // Populate form fields
                $('#event_title').val(event.title || '');
                $('#event_duration').val(event.duration || 60);
                $('#booking_per_slot').val(event.booking_per_slot || 1);

                // Set minimum notice using the new format
                setMinimumNoticeFromMinutes(event.minimum_notice || 0);

                $('#event_description').val(event.description || '');
                $('#custom_redirect_url').val(event.custom_redirect_url || '');

                // Handle location data - check if new format exists, otherwise use legacy format
                selectedLocations = [];

                if (event.locations_data) {
                    // New format - parse JSON data
                    try {
                        selectedLocations = JSON.parse(event.locations_data);
                    } catch (e) {
                        console.error('Error parsing locations_data:', e);
                        selectedLocations = [];
                    }
                } else if (event.location) {
                    // Legacy format - convert to new format
                    const legacyLocation = {
                        type: event.location,
                        display: getLocationDisplayName(event.location),
                        value: ''
                    };

                    // Set the value based on location type
                    if (event.location === 'in_person') {
                        legacyLocation.value = event.physical_address || '';
                    } else if (['zoom', 'skype', 'meet', 'others'].includes(event.location)) {
                        legacyLocation.value = event.meet_link || '';
                    }

                    if (legacyLocation.value) {
                        selectedLocations = [legacyLocation];
                    }
                }

                // Update the location display
                updateSelectedLocationsDisplay();

                // Populate custom fields if they exist
                populateCustomFieldsForEdit(event);

                // Show modal
                $('#createEventModal').modal('show');
            } else {
                show_toastr('error', response.message || 'Failed to load event');
            }
        },
        error: function(xhr) {
            console.log('Error loading event:', xhr.responseText);
            show_toastr('error', 'Error loading event details. Please try again.');
        }
    });
}



// Switch between views
function switchView(viewType) {
    console.log('switchView called with viewType:', viewType);

    try {
        // Remove active classes and set all buttons to inactive state
        $('#calendar-btn, #events-btn, #appointment-btn, #bookings-btn').each(function() {
            console.log('Resetting button:', this.id);
            $(this).removeClass('active btn-primary bg-primary').addClass('btn-outline-primary');
        });

        // Add active class and styling to the clicked button
        let activeBtn;
        if(viewType === 'calendar') {
            activeBtn = $('#calendar-btn');
        } else if(viewType === 'events') {
            activeBtn = $('#events-btn');
        } else if(viewType === 'appointment') {
            activeBtn = $('#appointment-btn');
        } else if(viewType === 'bookings') {
            activeBtn = $('#bookings-btn');
        }

        if(activeBtn && activeBtn.length) {
            console.log('Setting active button:', activeBtn.attr('id'));
            activeBtn.removeClass('btn-outline-primary').addClass('active btn-primary bg-primary');
        } else {
            console.error('Active button not found for viewType:', viewType);
        }
    } catch(error) {
        console.error('Error in switchView:', error);
    }

    // Show/hide sections
    $('#calendar-section, #events-section, #appointment-section, #bookings-section').hide();
    $('#' + viewType + '-section').show();
    // Show/hide create button
    const createBtn = $('#create-event-btn');
    if (viewType === 'events') {
        createBtn.removeClass('d-none');
        loadEventsList();
    } else {
        createBtn.addClass('d-none');
    }
    // Refresh calendar if switching to calendar view
    if (viewType === 'calendar' && calendar) {
        calendar.refetchEvents();
    }
}
// Load events list
function loadEventsList() {
    console.log('Loading events list...');
    
    $.ajax({
        url: '{{ route("calendar-events.index") }}',
        method: 'GET',
        success: function(response) {
            console.log('Events loaded:', response);
            
            const tbody = $('#events-table tbody');
            tbody.empty();
            
            if (response.success && response.data && response.data.length > 0) {
                response.data.forEach(function(event) {
                    // Add null check for event and required properties
                    if (!event || !event.id || !event.start_date || !event.end_date) {
                        console.warn('Skipping invalid event in events table:', event);
                        return;
                    }

                    // Format date range
                    const startDate = new Date(event.start_date).toLocaleDateString();
                    const endDate = new Date(event.end_date).toLocaleDateString();
                    const dateRange = startDate === endDate ? startDate : `${startDate} - ${endDate}`;

                    const row = `
                        <tr>
                            <td><strong>${event.title}</strong></td>
                            <td>${dateRange}</td>
                            <td>${event.duration || 60} min</td>
                            <td>${event.location || '-'}</td>
                            <td>Active</td>
                            <td>
                                <div class="d-flex flex-column">
                                    <span class="badge bg-success">${event.booking_per_slot || 1} slots</span>
                                    ${event.booking_count > 0 ? `<small class="text-muted mt-1">${event.booking_count} booked</small>` : ''}
                                </div>
                            </td>
                            <td>
                                <!-- View Button -->
                                <div style="position: relative; display: inline-block; margin-right: 6px;">
                                    <button onclick="viewEvent(${event.id})"
                                        style="background: linear-gradient(to right, #0d6efd, #6610f2); border: none; padding: 12px 12px; border-radius: 8px; display: inline-flex; align-items: center; justify-content: center; color: white; transition: transform 0.2s ease, box-shadow 0.2s ease;"
                                        onmouseover="this.style.transform='scale(1.1)'; this.style.boxShadow='0 4px 12px rgba(0,0,0,0.15)'; this.nextElementSibling.style.visibility='visible';"
                                        onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='none'; this.nextElementSibling.style.visibility='hidden';">
                                        <i class="ti ti-eye"></i>
                                    </button>
                                    <span style="visibility: hidden; background-color: black; color: #fff; text-align: center; padding: 4px 8px; border-radius: 4px; position: absolute; top: -30px; left: 50%; transform: translateX(-50%); white-space: nowrap; font-size: 12px; pointer-events: none;">
                                        View
                                    </span>
                                </div>

                                <!-- Edit Button -->
                                <div style="position: relative; display: inline-block; margin-right: 6px;">
                                    <button onclick="editEvent(${event.id})"
                                        style="background: linear-gradient(to right, #0f5132, #198754); border: none; padding: 12px 12px; border-radius: 8px; display: inline-flex; align-items: center; justify-content: center; color: white; transition: transform 0.2s ease, box-shadow 0.2s ease;"
                                        onmouseover="this.style.transform='scale(1.1)'; this.style.boxShadow='0 4px 12px rgba(0,0,0,0.15)'; this.nextElementSibling.style.visibility='visible';"
                                        onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='none'; this.nextElementSibling.style.visibility='hidden';">
                                        <i class="ti ti-pencil"></i>
                                    </button>
                                    <span style="visibility: hidden; background-color: black; color: #fff; text-align: center; padding: 4px 8px; border-radius: 4px; position: absolute; top: -30px; left: 50%; transform: translateX(-50%); white-space: nowrap; font-size: 12px;">
                                        Edit
                                    </span>
                                </div>

                                <!-- Copy Link Button -->
                                <div style="position: relative; display: inline-block; margin-right: 6px;">
                                    <button onclick="copyEventLinkFromList(${event.id}, this)"
                                        style="background: linear-gradient(to right, #6f42c1, #e83e8c); border: none; padding: 12px 12px; border-radius: 8px; display: inline-flex; align-items: center; justify-content: center; color: white; transition: transform 0.2s ease, box-shadow 0.2s ease;"
                                        onmouseover="this.style.transform='scale(1.1)'; this.style.boxShadow='0 4px 12px rgba(0,0,0,0.15)'; this.nextElementSibling.style.visibility='visible';"
                                        onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='none'; this.nextElementSibling.style.visibility='hidden';">
                                        <i class="ti ti-copy"></i>
                                    </button>
                                    <span style="visibility: hidden; background-color: black; color: #fff; text-align: center; padding: 4px 8px; border-radius: 4px; position: absolute; top: -30px; left: 50%; transform: translateX(-50%); white-space: nowrap; font-size: 12px;">
                                        Copy Link
                                    </span>
                                </div>

                                <!-- Delete Button -->
                                <div style="position: relative; display: inline-block;">
                                    <button onclick="deleteEvent(${event.id})"
                                        style="background: white; border: 1px solid #dc3545; padding: 10px 12px; border-radius: 8px; display: inline-flex; align-items: center; justify-content: center; color: #dc3545; transition: transform 0.2s ease, box-shadow 0.2s ease;"
                                        onmouseover="this.style.transform='scale(1.1)'; this.style.boxShadow='0 4px 12px rgba(0,0,0,0.15)'; this.nextElementSibling.style.visibility='visible';"
                                        onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='none'; this.nextElementSibling.style.visibility='hidden';">
                                        <i class="ti ti-trash"></i>
                                    </button>
                                    <span style="visibility: hidden; background-color: black; color: #fff; text-align: center; padding: 4px 8px; border-radius: 4px; position: absolute; top: -30px; left: 50%; transform: translateX(-50%); white-space: nowrap; font-size: 12px;">
                                        Delete
                                    </span>
                                </div>
                            </td>
                        </tr>
                    `;
                    tbody.append(row);
                });
            } else {
                // Update all counters to 0
                $('#events-count').text('0 {{ __("Events") }}');
                $('#active-events-count').text('0 {{ __("Active") }}');
                $('#total-bookings-count').text('0 {{ __("Bookings") }}');
                tbody.html('<tr><td colspan="7" class="text-center">No events found</td></tr>');
            }
        },
        error: function(xhr) {
            console.log('Error loading events:', xhr.responseText);
            // Update all counters to 0 on error
            $('#events-count').text('0 {{ __("Events") }}');
            $('#active-events-count').text('0 {{ __("Active") }}');
            $('#total-bookings-count').text('0 {{ __("Bookings") }}');
            $('#events-table tbody').html('<tr><td colspan="7" class="text-center text-danger">Error loading events</td></tr>');
        }
    });
}

// View bookings for a specific event
function viewBookings(eventId) {
    $.ajax({
        url: '{{ route("appointments.index") }}',
        method: 'GET',
        data: {
            event_id: eventId
        },
        success: function(response) {
            if (response.success && response.data) {
                let bookingsHtml = '<div class="table-responsive"><table class="table table-sm">';
                bookingsHtml += '<thead><tr><th>Contact Name</th><th>Date</th><th>Time</th><th>Timezone</th></tr></thead><tbody>';

                if (response.data.length > 0) {
                    response.data.forEach(function(booking) {
                        const timezone = booking.custom_fields && booking.custom_fields.timezone ? booking.custom_fields.timezone : 'Not specified';
                        bookingsHtml += `
                            <tr>
                                <td>${booking.name}</td>
                                <td>${new Date(booking.date).toLocaleDateString()}</td>
                                <td>${booking.time}</td>
                                <td><small>${timezone}</small></td>
                            </tr>
                        `;
                    });
                } else {
                    bookingsHtml += '<tr><td colspan="4" class="text-center">No bookings found</td></tr>';
                }

                bookingsHtml += '</tbody></table></div>';

                // Show in a modal or alert
                const modal = `
                    <div class="modal fade" id="bookingsModal" tabindex="-1">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">Event Bookings</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                </div>
                                <div class="modal-body">
                                    ${bookingsHtml}
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                // Remove existing modal if any
                $('#bookingsModal').remove();

                // Add modal to body and show
                $('body').append(modal);
                $('#bookingsModal').modal('show');
            }
        },
        error: function(xhr) {
            alert('Error loading bookings: ' + xhr.responseText);
        }
    });
}

function collectDateOverrideData() {
    const overrides = [];
    document.querySelectorAll('input[name="date_override[]"]').forEach(input => {
        overrides.push({ datetime: input.value });
    });
    return overrides;
}





// View event details in modal
function viewEvent(eventId) {
    console.log('Loading event details for ID:', eventId);

    // Validate eventId
    if (!eventId) {
        console.error('No event ID provided');
        showEventError('Invalid event ID');
        return;
    }

    // Show loading state
    $('#view-event-title').text('Loading...');
    $('#view-event-duration').text('');
    $('#view-event-booking-slots').text('');
    $('#view-event-notice').text('');
    $('#view-description-row').hide();
    $('#view-location-section').hide();
    $('#viewEventModal').modal('show');

    // First, try to get the event from the calendar events index
    $.ajax({
        url: '{{ route("calendar-events.index") }}',
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        timeout: 10000, // 10 second timeout
        success: function(response) {
            console.log('Events response:', response);

            if (response.success && response.data && Array.isArray(response.data)) {
                // Find the specific event by ID
                const event = response.data.find(e => e.id == eventId);

                if (event) {
                    console.log('Found event:', event);
                    populateEventModal(event, eventId);
                } else {
                    console.error('Event not found in response. Available events:', response.data.map(e => e.id));
                    showEventError('Event not found. The event may have been deleted or you may not have permission to view it.');
                }
            } else if (response.success === false) {
                console.error('API returned error:', response.message);
                showEventError(response.message || 'Failed to load event details');
            } else {
                console.error('Invalid response format:', response);
                showEventError('Invalid response from server');
            }
        },
        error: function(xhr, status, error) {
            console.error('AJAX error:', {xhr, status, error});

            let errorMessage = 'Error loading event details';

            if (xhr.status === 403) {
                errorMessage = 'You do not have permission to view this event';
            } else if (xhr.status === 404) {
                errorMessage = 'Event not found';
            } else if (xhr.status === 500) {
                errorMessage = 'Server error occurred';
            } else if (status === 'timeout') {
                errorMessage = 'Request timed out. Please try again.';
            } else if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            }

            showEventError(errorMessage);
        }
    });
}

// Helper function to populate the event modal
function populateEventModal(event, eventId) {
    try {
        // Validate event object
        if (!event || typeof event !== 'object') {
            console.error('Invalid event object:', event);
            showEventError('Invalid event data');
            return;
        }

        // Check if modal elements exist
        const requiredElements = [
            '#view-event-title',
            '#view-event-duration', '#view-event-booking-slots', '#view-event-notice'
        ];

        for (const elementId of requiredElements) {
            if ($(elementId).length === 0) {
                console.error('Required modal element not found:', elementId);
                showEventError('Modal elements not found. Please refresh the page.');
                return;
            }
        }

        // Set the event ID for copying the link
        $('#viewEventModal').data('event-id', eventId);

        // Populate basic event details
        $('#view-event-title').text(event.title || 'Untitled Event');



        // Event settings with safe defaults
        const duration = event.duration || 60;
        const bookingSlots = event.booking_per_slot || 1;
        const minimumNotice = event.minimum_notice || 0;

        $('#view-event-duration').text(duration + ' minutes');
        $('#view-event-booking-slots').text(bookingSlots);
        $('#view-event-notice').text(getNoticeText(minimumNotice));

        // Description
        if (event.description && typeof event.description === 'string' && event.description.trim()) {
            $('#view-event-description').text(event.description);
            $('#view-description-row').show();
        } else {
            $('#view-description-row').hide();
        }

        // Location details - handle new multi-location system
        let hasLocations = false;

        if (event.locations_data) {
            // New multi-location system
            try {
                const locations = JSON.parse(event.locations_data);
                if (locations && locations.length > 0) {
                    hasLocations = true;

                    // Display all locations
                    const locationDisplay = locations.map(loc => loc.display).join(', ');
                    $('#view-event-location').text(locationDisplay);
                    $('#view-location-section').show();

                    // Show details for each location type
                    let hasAddress = false;
                    let hasLink = false;
                    let addressText = '';
                    let linkText = '';
                    let linkHref = '#';

                    locations.forEach(location => {
                        if (location.type === 'in_person' && location.value) {
                            hasAddress = true;
                            addressText += (addressText ? '\n' : '') + location.value;
                        } else if (['zoom', 'meet', 'skype', 'phone', 'others'].includes(location.type) && location.value) {
                            hasLink = true;
                            linkText += (linkText ? ', ' : '') + location.value;
                            // Format phone numbers as tel: links
                            if (linkHref === '#') {
                                if (location.type === 'phone' && /^[\+]?[0-9\s\-\(\)]+$/.test(location.value.trim())) {
                                    linkHref = 'tel:' + location.value.trim();
                                } else {
                                    linkHref = location.value;
                                }
                            }
                        }
                    });

                    // Show address if any in-person locations
                    if (hasAddress) {
                        $('#view-event-address').text(addressText);
                        $('#view-address-row').show();
                    } else {
                        $('#view-address-row').hide();
                    }

                    // Show all online locations
                    const onlineLocations = locations.filter(loc => ['zoom', 'meet', 'skype', 'phone', 'others'].includes(loc.type));
                    if (onlineLocations.length > 0) {
                        const locationIcons = {
                            'zoom': 'ti ti-video',
                            'meet': 'ti ti-brand-google',
                            'skype': 'ti ti-brand-skype',
                            'phone': 'ti ti-phone',
                            'others': 'ti ti-dots'
                        };

                        let meetingLocationsHtml = '';
                        onlineLocations.forEach((location, index) => {
                            const icon = locationIcons[location.type] || 'ti ti-link';
                            const href = location.type === 'phone' && /^[\+]?[0-9\s\-\(\)]+$/.test(location.value.trim())
                                ? 'tel:' + location.value.trim()
                                : location.value;

                            const chipClass = `modern-location-chip ${location.type}-chip`;

                            meetingLocationsHtml += `
                                <a href="${href}" target="_blank" class="${chipClass}" onclick="handleMeetingLinkClick(event)">
                                    <i class="location-icon ${icon}"></i>
                                    <span class="location-name">${location.display || location.type}</span>
                                </a>
                            `;
                        });

                        $('#view-meeting-locations-container').html(meetingLocationsHtml);
                        $('#view-link-row').show();
                    } else {
                        $('#view-link-row').hide();
                    }

                    // Set dynamic column classes based on what's visible
                    updateLocationColumnLayout();
                }
            } catch (e) {
                console.error('Error parsing locations_data:', e);
                $('#view-event-location').text('Location data error');
                $('#view-location-section').show();
                $('#view-address-row').hide();
                $('#view-link-row').hide();
                hasLocations = true;
            }
        } else if (event.location && typeof event.location === 'string') {
            // Legacy single location system
            hasLocations = true;
            const locationLabels = {
                'in_person': 'In Person',
                'zoom': 'Zoom',
                'skype': 'Skype',
                'meet': 'Google Meet',
                'phone': 'Phone Call',
                'others': 'Others'
            };

            $('#view-event-location').text(locationLabels[event.location] || event.location);
            $('#view-location-section').show();

            // Handle physical address or meeting link
            if (event.location === 'in_person') {
                if (event.physical_address && typeof event.physical_address === 'string' && event.physical_address.trim()) {
                    $('#view-event-address').text(event.physical_address);
                    $('#view-address-row').show();
                } else {
                    $('#view-event-address').text('Address not specified');
                    $('#view-address-row').show();
                }
                $('#view-link-row').hide();
            } else {
                // For online meetings and phone calls
                const locationIcons = {
                    'zoom': 'ti ti-video',
                    'meet': 'ti ti-brand-google',
                    'skype': 'ti ti-brand-skype',
                    'phone': 'ti ti-phone',
                    'others': 'ti ti-dots'
                };

                let meetingLocationsHtml = '';
                const icon = locationIcons[event.location] || 'ti ti-link';
                const locationName = locationLabels[event.location] || event.location;

                const chipClass = `modern-location-chip ${event.location}-chip`;

                if (event.meet_link && typeof event.meet_link === 'string' && event.meet_link.trim() && event.meet_link !== '#') {
                    const meetLink = event.meet_link.trim();
                    const href = /^[\+]?[0-9\s\-\(\)]+$/.test(meetLink)
                        ? 'tel:' + meetLink
                        : meetLink;

                    meetingLocationsHtml = `
                        <a href="${href}" target="_blank" class="${chipClass}" onclick="handleMeetingLinkClick(event)">
                            <i class="location-icon ${icon}"></i>
                            <span class="location-name">${locationName}</span>
                        </a>
                    `;
                } else {
                    meetingLocationsHtml = `
                        <div class="${chipClass}" style="cursor: default; opacity: 0.7;">
                            <i class="location-icon ${icon}"></i>
                            <span class="location-name">${locationName}</span>
                            <small class="ms-2 text-muted">(No link)</small>
                        </div>
                    `;
                }

                $('#view-meeting-locations-container').html(meetingLocationsHtml);
                $('#view-link-row').show();
                $('#view-address-row').hide();

                // Set dynamic column classes
                updateLocationColumnLayout();
            }
        }

        if (!hasLocations) {
            $('#view-location-section').hide();
        }

        console.log('Event modal populated successfully');

    } catch (error) {
        console.error('Error populating event modal:', error);
        showEventError('Error displaying event details: ' + error.message);
    }
}

// Helper function to update location column layout dynamically
function updateLocationColumnLayout() {
    const hasAddress = $('#view-address-row').is(':visible');
    const hasOnlineLocations = $('#view-link-row').is(':visible');

    // Remove existing column classes
    $('#view-address-row, #view-link-row').removeClass('col-12 col-md-6 col-lg-4');

    if (hasAddress && hasOnlineLocations) {
        // Both types present - use two columns
        $('#view-address-row, #view-link-row').addClass('col-md-6');
    } else if (hasAddress || hasOnlineLocations) {
        // Only one type present - use full width
        $('#view-address-row, #view-link-row').addClass('col-12');
    }
}

// Helper function to show error in modal
function showEventError(message) {
    $('#view-event-title').text('Error');
    $('#view-event-duration').text('N/A');
    $('#view-event-booking-slots').text('N/A');
    $('#view-event-notice').text('N/A');
    $('#view-description-row').hide();
    $('#view-location-section').hide();
}

// Helper function to format notice text
function getNoticeText(minutes) {
    // Handle null, undefined, or invalid values
    if (!minutes || isNaN(minutes) || minutes <= 0) {
        return 'No notice required';
    }

    // Convert to number if it's a string
    const numMinutes = parseInt(minutes);

    if (isNaN(numMinutes) || numMinutes <= 0) {
        return 'No notice required';
    }

    const hours = Math.floor(numMinutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
        const remainingHours = hours % 24;
        if (remainingHours > 0) {
            return `${days} day${days > 1 ? 's' : ''} ${remainingHours} hour${remainingHours > 1 ? 's' : ''}`;
        }
        return `${days} day${days > 1 ? 's' : ''}`;
    }

    if (hours > 0) {
        const remainingMinutes = numMinutes % 60;
        if (remainingMinutes > 0) {
            return `${hours} hour${hours > 1 ? 's' : ''} ${remainingMinutes} minute${remainingMinutes > 1 ? 's' : ''}`;
        }
        return `${hours} hour${hours > 1 ? 's' : ''}`;
    }

    return `${numMinutes} minute${numMinutes > 1 ? 's' : ''}`;
}

// Delete event
function deleteEvent(eventId) {
    if (confirm('Are you sure you want to delete this event?')) {
        $.ajax({
            url: `{{ url('calendar-events') }}/${eventId}`,
            method: 'DELETE',
            data: {
                "_token": "{{ csrf_token() }}"
            },
            success: function(response) {
                if (response.success) {
                    show_toastr('success', 'Event deleted successfully!');
                    loadEventsList();
                    loadAllEvents(); // Refresh sidebar events
                    if (calendar) {
                        calendar.refetchEvents();
                    }
                } else {
                    show_toastr('error', response.message || 'Failed to delete event');
                }
            },
            error: function(xhr) {
                console.log('Delete error:', xhr.responseText);
                show_toastr('error', 'Error deleting event. Please try again.');
            }
        });
    }
}

// Make functions global
window.openCreateEventModal = openCreateEventModal;
window.switchView = switchView;
window.deleteEvent = deleteEvent;
window.viewEvent = viewEvent;
window.editEvent = editEvent;
window.toggleLocationFields = toggleLocationFields;
window.collectDateOverrideData = collectDateOverrideData;
window.showCopyEventToast = showCopyEventToast;
window.hideCopyEventToast = hideCopyEventToast;

// Test function for copy event link
window.testCopyEventLink = function() {
    console.log('Testing copy event link functionality...');
    console.log('copyEventLinkFromList function exists:', typeof window.copyEventLinkFromList);

    // Test the copy function with a dummy event ID
    if (typeof window.copyEventLinkFromList === 'function') {
        console.log('Testing copyEventLinkFromList with dummy data...');
        // Create a temporary test button
        const testBtn = document.createElement('button');
        testBtn.innerHTML = '<i class="ti ti-copy"></i>';
        window.copyEventLinkFromList(123, testBtn);
    }
};

//weekly available
// Initialize with one slot per day when enabled
$('.day-availability input[type="checkbox"]').change(function() {
    const day = $(this).closest('.day-availability').find('.day-slots');
    if (this.checked) {
        day.show();
        if (day.find('.time-slot').length === 0) {
            addNewSlot($(this).closest('.day-availability').attr('id').replace('-availability', ''));
        }
    } else {
        day.hide();
    }
});

// Add slot functionality
$('.add-slot-btn').click(function() {
    const day = $(this).data('day');
    addNewSlot(day);
});

// Remove slot functionality
$(document).on('click', '.remove-slot-btn', function() {
    if ($(this).closest('.day-slots').find('.time-slot').length > 1) {
        $(this).closest('.time-slot').remove();
    } else {
        alert('Each day must have at least one time slot');
    }
});

// Helper function to add new slot
function addNewSlot(day) {
    const slotCount = $(`#${day}-slots .time-slot`).length;
    const newSlot = `
        <div class="time-slot">
            <input type="time" name="availability[${day}][slots][${slotCount}][start]" class="form-control" required>
            <span class="mx-2">to</span>
            <input type="time" name="availability[${day}][slots][${slotCount}][end]" class="form-control" required>
            <button type="button" class="btn btn-sm btn-outline-danger remove-slot-btn">
                <i class="ti ti-trash"></i>
            </button>
        </div>
    `;
    $(`#${day}-slots`).append(newSlot);
}

//date override
let unavailableSlots = [];

// Function to toggle the visibility of the date override field
function toggleDateOverride() {
    const overrideField = document.getElementById('date-override-field');
    if (overrideField.style.display === 'none') {
        overrideField.style.display = 'block';
    } else {
        overrideField.style.display = 'none';
    }
}

// Function to add the selected date and time as an unavailable slot
function addUnavailableSlot() {
    const input = document.getElementById('override_date');
    const container = document.getElementById('unavailable-slots-list');
    const value = input.value;

    if (!value) {
        alert("Please select a date and time.");
        return;
    }

    // Check for duplicates
    const exists = [...document.querySelectorAll('input[name="date_override[]"]')]
        .some(i => i.value === value);
    if (exists) {
        alert("This slot is already added.");
        return;
    }

    const wrapper = document.createElement('div');
    wrapper.className = 'd-flex align-items-center mb-2 gap-2';

    const readable = new Date(value).toLocaleString('en-IN', {
        dateStyle: 'medium',
        timeStyle: 'short'
    });

    wrapper.innerHTML = `
        <input type="hidden" name="date_override[]" value="${value}">
        <span class="badge bg-secondary flex-grow-1 py-2 px-3">${readable}</span>
        <button type="button" class="btn btn-sm btn-danger" onclick="this.parentElement.remove()">×</button>
    `;

    container.appendChild(wrapper);
    input.value = '';
}


// Function to update the unavailable slots list
function updateUnavailableSlotsList() {
    const slotsList = document.getElementById('unavailable-slots-list');
    slotsList.innerHTML = ''; // Clear the list

    unavailableSlots.forEach((slot, index) => {
        const listItem = document.createElement('li');
        listItem.textContent = slot.toLocaleString(); // Format the date for display

        // Create a delete button for each slot
        const deleteButton = document.createElement('button');
        deleteButton.textContent = 'Delete';
        deleteButton.className = 'btn btn-danger btn-sm ms-2';
        deleteButton.onclick = () => deleteUnavailableSlot(index); // Bind delete function

        listItem.appendChild(deleteButton);
        slotsList.appendChild(listItem);
    });
}

// Function to delete an unavailable slot
function deleteUnavailableSlot(index) {
    unavailableSlots.splice(index, 1); // Remove the slot from the array
    updateUnavailableSlotsList(); // Update the displayed list
    alert('The selected time slot has been removed.');
}

// Function to check if a selected time slot is available for booking
function isTimeSlotAvailable(selectedDate) {
    return !unavailableSlots.some(slot => slot.getTime() === selectedDate.getTime());
}

// Example function to create an appointment
function createAppointment(selectedDate) {
    if (!isTimeSlotAvailable(selectedDate)) {
        alert('This time slot is unavailable. Please choose a different time.');
        return;
    }

    // Proceed with appointment creation logic
    // For example, you can send the appointment data to the server here
    alert(`Appointment created for ${selectedDate.toLocaleString()}`);
}

// Show custom toast for copy event link
function showCopyEventToast(message = 'Event link copied!') {
    console.log('showCopyEventToast called with message:', message);

    const toast = document.getElementById('copy-event-toast');
    const msg = document.getElementById('copy-event-toast-message');

    if (toast && msg) {
        msg.textContent = message;
        toast.style.display = 'block';
        toast.setAttribute('aria-hidden', 'false');
        // Auto-hide after 2.5s
        clearTimeout(window._copyEventToastTimeout);
        window._copyEventToastTimeout = setTimeout(hideCopyEventToast, 2500);
        console.log('Toast displayed successfully');
    } else {
        // Fallback to alert if toast elements not found
        console.log('Toast elements not found, using alert fallback');
        alert(message);
    }
}
function hideCopyEventToast() {
    const toast = document.getElementById('copy-event-toast');
    if (toast) {
        toast.style.display = 'none';
        toast.setAttribute('aria-hidden', 'true');
    }
}
// Copy event link function for Events List (with direct event ID)
window.copyEventLinkFromList = function(eventId, btn) {
    console.log('copyEventLinkFromList called with eventId:', eventId, 'button:', btn);

    if (!eventId) {
        console.error('No event ID provided');
        showCopyEventToast('No event ID provided');
        updateCopyButtonList(btn, false);
        return;
    }

    // Copy the event page link
    const linkToCopy = `${window.location.origin}/calendar-events/${eventId}`;
    const successMessage = 'Event link copied!';

    console.log('Copying link:', linkToCopy);

    // Check if clipboard API is available and we're in a secure context
    const isSecureContext = window.isSecureContext || location.protocol === 'https:' || location.hostname === 'localhost';
    console.log('Secure context:', isSecureContext);
    console.log('Clipboard API available:', !!navigator.clipboard);

    if (!navigator.clipboard || !isSecureContext) {
        console.log('Using fallback copy method');
        // Fallback for older browsers or non-secure contexts
        fallbackCopyTextToClipboardList(linkToCopy, btn, successMessage);
        return;
    }

    // Use modern clipboard API
    console.log('Using modern clipboard API');
    navigator.clipboard.writeText(linkToCopy).then(function() {
        console.log('Link copied successfully');
        showCopyEventToast(successMessage);
        updateCopyButtonList(btn, true);
    }).catch(function(err) {
        console.error('Failed to copy link with clipboard API:', err);
        console.log('Falling back to legacy copy method');
        // Try fallback method if clipboard API fails
        fallbackCopyTextToClipboardList(linkToCopy, btn, successMessage);
    });
}

// Enhanced copyEventLink function with better error handling (for modal - kept for backward compatibility)
window.copyEventLink = function(btn) {
    console.log('copyEventLink called with button:', btn);
    console.log('Button element:', $(btn));
    console.log('Modal element:', $('#viewEventModal'));

    // Get current event ID from the modal
    const eventId = $('#viewEventModal').data('event-id');
    console.log('Event ID from modal:', eventId);
    console.log('Modal data attributes:', $('#viewEventModal').data());

    if (!eventId) {
        console.error('No event ID found');
        showCopyEventToast('No event selected to copy link');
        updateCopyButton(btn, false);
        return;
    }

    // Copy the event page link
    const linkToCopy = `${window.location.origin}/calendar-events/${eventId}`;
    const successMessage = 'Event link copied!';

    console.log('Copying link:', linkToCopy);

    // Check if clipboard API is available and we're in a secure context
    const isSecureContext = window.isSecureContext || location.protocol === 'https:' || location.hostname === 'localhost';
    console.log('Secure context:', isSecureContext);
    console.log('Clipboard API available:', !!navigator.clipboard);

    if (!navigator.clipboard || !isSecureContext) {
        console.log('Using fallback copy method');
        // Fallback for older browsers or non-secure contexts
        fallbackCopyTextToClipboard(linkToCopy, btn, successMessage);
        return;
    }

    // Use modern clipboard API
    console.log('Using modern clipboard API');
    navigator.clipboard.writeText(linkToCopy).then(function() {
        console.log('Link copied successfully');
        showCopyEventToast(successMessage);
        updateCopyButton(btn, true);
    }).catch(function(err) {
        console.error('Failed to copy link with clipboard API:', err);
        console.log('Falling back to legacy copy method');
        // Try fallback method if clipboard API fails
        fallbackCopyTextToClipboard(linkToCopy, btn, successMessage);
    });
}

// Fallback function for older browsers
function fallbackCopyTextToClipboard(text, btn, successMessage) {
    console.log('Using fallback copy method for text:', text);

    const textArea = document.createElement("textarea");
    textArea.value = text;

    // Avoid scrolling to bottom
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";
    textArea.style.opacity = "0";

    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        const successful = document.execCommand('copy');
        console.log('execCommand copy result:', successful);

        if (successful) {
            console.log('Fallback copy successful');
            showCopyEventToast(successMessage);
            updateCopyButton(btn, true);
        } else {
            console.log('Fallback copy failed');
            showCopyEventToast('Failed to copy link - please copy manually: ' + text);
            updateCopyButton(btn, false);
        }
    } catch (err) {
        console.error('Fallback: Could not copy text: ', err);
        showCopyEventToast('Copy not supported - Link: ' + text);
        updateCopyButton(btn, false);
    }

    document.body.removeChild(textArea);
}

// Fallback function for older browsers (Events List)
function fallbackCopyTextToClipboardList(text, btn, successMessage) {
    console.log('Using fallback copy method for list with text:', text);

    const textArea = document.createElement("textarea");
    textArea.value = text;

    // Avoid scrolling to bottom
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";
    textArea.style.opacity = "0";

    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        const successful = document.execCommand('copy');
        console.log('execCommand copy result for list:', successful);

        if (successful) {
            console.log('Fallback copy successful for list');
            showCopyEventToast(successMessage);
            updateCopyButtonList(btn, true);
        } else {
            console.log('Fallback copy failed for list');
            showCopyEventToast('Failed to copy link - please copy manually: ' + text);
            updateCopyButtonList(btn, false);
        }
    } catch (err) {
        console.error('Fallback: Could not copy text for list: ', err);
        showCopyEventToast('Copy not supported - Link: ' + text);
        updateCopyButtonList(btn, false);
    }

    document.body.removeChild(textArea);
}

// Helper function to update copy button appearance
function updateCopyButton(btn, success) {
    if (!btn) return;

    const $btn = $(btn);
    const originalHtml = $btn.html();

    if (success) {
        $btn.html('<i class="ti ti-check me-1"></i>Copied!')
            .removeClass('btn-outline-primary')
            .addClass('btn-success copy-link-dark-green')
            .prop('disabled', true);
    } else {
        $btn.html('<i class="ti ti-x me-1"></i>Failed')
            .removeClass('btn-outline-primary')
            .addClass('btn-danger')
            .prop('disabled', true);
    }

    // Reset button after 3 seconds
    setTimeout(function() {
        $btn.html(originalHtml)
            .removeClass('btn-success btn-danger copy-link-dark-green')
            .addClass('btn-outline-primary')
            .prop('disabled', false);
    }, 3000);
}

// Helper function to update copy button appearance for Events List
function updateCopyButtonList(btn, success) {
    if (!btn) return;

    const originalHtml = btn.innerHTML;
    const originalStyle = btn.style.background;

    if (success) {
        btn.innerHTML = '<i class="ti ti-check"></i>';
        btn.style.background = 'linear-gradient(to right, #198754, #20c997)';
        btn.disabled = true;
    } else {
        btn.innerHTML = '<i class="ti ti-x"></i>';
        btn.style.background = 'linear-gradient(to right, #dc3545, #fd7e14)';
        btn.disabled = true;
    }

    // Reset button after 3 seconds
    setTimeout(function() {
        btn.innerHTML = originalHtml;
        btn.style.background = originalStyle;
        btn.disabled = false;
    }, 3000);
}

// Handle meeting link click with validation
function handleMeetingLinkClick(event) {
    const link = event.target.closest('a').getAttribute('href');

    if (!link || link === '#' || link.trim() === '') {
        event.preventDefault();
        return false;
    }

    // Check if it's a phone number (starts with tel:)
    if (link.startsWith('tel:')) {
        // Allow default behavior for tel: links (will open phone app)
        return true;
    }

    // Validate if it's a proper URL
    try {
        new URL(link);
        // If URL is valid, allow the default behavior (open in new tab)
        return true;
    } catch (e) {
        event.preventDefault();
        return false;
    }
}

// Bookings Management Functions
function createNewBookingAction() {
    // Switch to calendar view and show a helpful message
    switchView('calendar');

    // Show a toast or alert to guide the user
    setTimeout(() => {
        showBookingsAlert('{{ __("To create a new booking, click on a date in the calendar and select an available time slot.") }}', 'info');
    }, 500);
}

function refreshBookings() {
    console.log('Refreshing bookings...');
    loadBookings();
}

function loadBookings() {
    console.log('Loading bookings...');

    // Show loading state
    $('#bookings-tbody').html(`
        <tr>
            <td colspan="{{ Auth::user() && Auth::user()->can('manage booking') ? '10' : '9' }}" class="text-center py-4">
                <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                {{ __('Loading bookings...') }}
            </td>
        </tr>
    `);

    // Since the bookings controller returns HTML view, we'll use a different approach
    // We'll load the bookings page content and extract the data
    $.ajax({
        url: '{{ route("bookings.index") }}',
        method: 'GET',
        success: function(response) {
            let tbody = $('#bookings-tbody');
            tbody.empty();

            try {
                // Parse the HTML response to extract booking data
                let $response = $(response);
                let $bookingRows = $response.find('#bookings-table tbody tr');

                if ($bookingRows.length > 0) {
                    // Check if it's the "no bookings" row
                    let firstRowText = $bookingRows.first().text().trim();
                    if (firstRowText.includes('No bookings found') || firstRowText.includes('{{ __("No bookings found") }}')) {
                        tbody.html(`
                            <tr>
                                <td colspan="{{ Auth::user() && Auth::user()->can('manage booking') ? '10' : '9' }}" class="text-center">{{ __('No bookings found') }}</td>
                            </tr>
                        `);
                    } else {
                        // Copy the rows from the original bookings page
                        $bookingRows.each(function() {
                            tbody.append($(this).clone());
                        });
                    }
                } else {
                    tbody.html(`
                        <tr>
                            <td colspan="{{ Auth::user() && Auth::user()->can('manage booking') ? '10' : '9' }}" class="text-center">{{ __('No bookings found') }}</td>
                        </tr>
                    `);
                }
            } catch (error) {
                console.error('Error parsing bookings data:', error);
                tbody.html(`
                    <tr>
                        <td colspan="{{ Auth::user() && Auth::user()->can('manage booking') ? '10' : '9' }}" class="text-center">
                            <div class="alert alert-info">
                                <i class="ti ti-info-circle me-2"></i>
                                {{ __('Unable to load bookings dynamically. Please') }}
                                <a href="{{ route('bookings.index') }}" target="_blank" class="alert-link">{{ __('click here') }}</a>
                                {{ __('to view bookings in a new tab.') }}
                            </div>
                        </td>
                    </tr>
                `);
            }
        },
        error: function(xhr) {
            console.error('Error loading bookings:', xhr);
            let tbody = $('#bookings-tbody');

            if (xhr.status === 403) {
                tbody.html(`
                    <tr>
                        <td colspan="{{ Auth::user() && Auth::user()->can('manage booking') ? '10' : '9' }}" class="text-center">
                            <div class="alert alert-warning">
                                <i class="ti ti-lock me-2"></i>
                                {{ __('You do not have permission to view bookings.') }}
                            </div>
                        </td>
                    </tr>
                `);
            } else {
                tbody.html(`
                    <tr>
                        <td colspan="{{ Auth::user() && Auth::user()->can('manage booking') ? '10' : '9' }}" class="text-center">
                            <div class="alert alert-danger">
                                <i class="ti ti-exclamation-triangle me-2"></i>
                                {{ __('Error loading bookings. Please') }}
                                <a href="{{ route('bookings.index') }}" target="_blank" class="alert-link">{{ __('click here') }}</a>
                                {{ __('to view bookings in a new tab.') }}
                            </div>
                        </td>
                    </tr>
                `);
            }
        }
    });
}

// Initialize default view on page load
$(document).ready(function() {
    // Set default active button styling
    switchView('calendar');

    // Load bookings when bookings section is shown
    $(document).on('click', '#bookings-btn', function() {
        setTimeout(() => {
            if ($('#bookings-section').is(':visible')) {
                loadBookings();
            }
        }, 100);
    });

    // Also load bookings if the page loads with bookings section visible
    if ($('#bookings-section').is(':visible')) {
        loadBookings();
    }
});

// Bookings Helper Functions
function showCustomFieldsModal(customFieldsData, contactName) {
    let content = '<div class="row">';

    // Add contact name header
    content += `
        <div class="col-12 mb-4">
            <div class="alert alert-primary">
                <h6 class="mb-0">
                    <i class="ti ti-user me-2"></i>Additional Info for: <strong>${contactName}</strong>
                </h6>
            </div>
        </div>
    `;

    if (customFieldsData && Array.isArray(customFieldsData) && customFieldsData.length > 0) {
        customFieldsData.forEach(function(field) {
            const icon = getFieldIcon(field.type);
            const formattedValue = formatFieldValue(field.type, field.value);

            content += `
                <div class="col-md-6 mb-3">
                    <div class="card custom-field-card h-100 border-primary">
                        <div class="card-body p-3">
                            <div class="d-flex align-items-start">
                                <div class="flex-shrink-0 me-3">
                                    <div class="custom-field-icon bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                        <i class="ti ti-${icon}"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-2 text-primary fw-bold">${field.label}</h6>
                                    <div class="field-value-container">
                                        <span class="badge bg-light text-dark fs-6 p-2 w-100 text-start">${formattedValue}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });
    } else {
        content += '<div class="col-12"><div class="alert alert-warning text-center"><i class="ti ti-info-circle me-2"></i>No Additional Info available for this booking</div></div>';
    }

    content += '</div>';

    $('#customFieldsContent').html(content);
    $('#customFieldsModal').modal('show');
}

function getFieldLabel(fieldKey) {
    const fieldLabels = {
        'contact_type': 'Contact Type',
        'date_of_birth': 'Date of Birth',
        'business_type': 'Business Type',
        'business_gst_number': 'Business GST Number',
        'lead_value': 'Lead Value',
        'assigned_to_staff': 'Assigned to Staff',
        'contact_source': 'Contact Source',
        'opportunity_name': 'Opportunity Name',
        'postal_code': 'Postal Code',
        'full_name': 'Full Name',
        'specific_requirement': 'Any Specific Requirement',
        'used_whatsapp_api_chatbots': 'Have you used WhatsApp API and Chatbots ever',
        'generate_leads': 'How do you generate leads',
        'hear_about_omx_sales': 'Where did you hear about OMX Sales?',
        'city': 'City',
        'have_msme_certificate': 'Do you have MSME Certificate?',
        'whatsapp_number': 'WhatsApp Number',
        'meta_business_name': 'META Business Name',
        'have_website': 'Do you have a website?',
        'business_industry': 'Business Industry',
        'message': 'Message',
        'organization_task': 'Organization Task',
        'team_size': 'Team Size',
        'company_revenue': 'Company Revenue',
        'budget': 'What is your budget?',
        'real_estate_services': 'What type of real estate services do you offer?',
        'using_chatbot_tools': 'Are you currently using any chatbot or automation tools?',
        'implement_chatbot_timeframe': 'How soon are you looking to implement a chatbot?',
        'running_digital_ads': 'Are you currently running any digital ads?',
        'monthly_advertising_budget': 'Monthly Advertising Budget',
        'promoted_projects_count': 'How many real estate projects are you promoting?',
        'biggest_marketing_challenge': 'What is your biggest marketing challenge?',
        'property_price_range': 'What is the price range of the property you are selling?',
        'using_crm_software': 'Are you currently using any CRM software?',
        'advertising_on_third_party_platforms': 'Are you advertising on any third-party platforms?',
        'know_whatsapp_api': 'Do you know about the WhatsApp API?',
        'messages_volume': 'How many messages do you need to send?',
        'using_whatsapp_official_api': 'How are you currently doing WhatsApp Official API?',
        'monthly_lead_sales_volume': 'Monthly Lead/Sales Volume'
    };

    return fieldLabels[fieldKey] || fieldKey.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
}

function formatFieldValue(fieldKey, value) {
    // Format specific field types
    if (fieldKey === 'date_of_birth' && value) {
        const date = new Date(value);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    }

    if ((fieldKey.includes('budget') || fieldKey.includes('revenue') || fieldKey.includes('value')) && value) {
        // Format currency values
        if (!isNaN(value)) {
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD'
            }).format(value);
        }
    }

    if (fieldKey === 'team_size' && value) {
        return value + ' employees';
    }

    if ((fieldKey.includes('have_') || fieldKey.includes('using_') || fieldKey.includes('know_')) && value) {
        return value.charAt(0).toUpperCase() + value.slice(1);
    }

    return value || 'Not specified';
}

function getFieldIcon(fieldKey) {
    const fieldIcons = {
        'contact_type': 'phone',
        'date_of_birth': 'calendar',
        'business_type': 'building-store',
        'business_gst_number': 'file-text',
        'lead_value': 'currency-dollar',
        'assigned_to_staff': 'user',
        'contact_source': 'source',
        'opportunity_name': 'target',
        'postal_code': 'map-pin',
        'full_name': 'user-circle',
        'specific_requirement': 'list-details',
        'used_whatsapp_api_chatbots': 'brand-whatsapp',
        'generate_leads': 'trending-up',
        'hear_about_omx_sales': 'ear',
        'city': 'map-pin',
        'have_msme_certificate': 'certificate',
        'whatsapp_number': 'brand-whatsapp',
        'meta_business_name': 'brand-facebook',
        'have_website': 'world-www',
        'business_industry': 'building',
        'message': 'message',
        'organization_task': 'checklist',
        'team_size': 'users',
        'company_revenue': 'chart-line',
        'budget': 'wallet',
        'real_estate_services': 'home',
        'using_chatbot_tools': 'robot',
        'implement_chatbot_timeframe': 'clock',
        'running_digital_ads': 'ad',
        'monthly_advertising_budget': 'currency-dollar',
        'promoted_projects_count': 'hash',
        'biggest_marketing_challenge': 'alert-triangle',
        'property_price_range': 'home-dollar',
        'using_crm_software': 'database',
        'advertising_on_third_party_platforms': 'external-link',
        'know_whatsapp_api': 'brand-whatsapp',
        'messages_volume': 'message-circle',
        'using_whatsapp_official_api': 'api',
        'monthly_lead_sales_volume': 'chart-bar'
    };
    return fieldIcons[fieldKey] || 'info-circle';
}
// Alert system functions for bookings
function showBookingsAlert(message, type = 'success') {
    const alertContainer = $('#bookings-alert-container');
    const alertMessage = $('#bookings-alert-message');
    const alertText = $('#bookings-alert-text');
    // Set alert type and message
    alertMessage.removeClass('alert-success alert-danger alert-warning alert-info');
    alertMessage.addClass(`alert-${type}`);
    alertText.text(message);

    // Show alert
    alertContainer.show();

    // Auto-hide after 5 seconds
    setTimeout(function() {
        alertContainer.fadeOut();
    }, 5000);
}

// Admin CRUD Functions for Bookings
@can('manage booking')

// View booking details
function viewBooking(bookingId) {
    $.ajax({
        url: `{{ url('bookings') }}/${bookingId}`,
        method: 'GET',
        success: function(response) {
            if (response.success) {
                const booking = response.data;
                let content = `
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="card border-primary">
                                <div class="card-body">
                                    <h6 class="card-title text-primary"><i class="ti ti-user me-2"></i>Customer Information</h6>
                                    <p><strong>Name:</strong> ${booking.name}</p>
                                    <p><strong>Email:</strong> ${booking.email}</p>
                                    <p><strong>Phone:</strong> ${booking.phone || 'N/A'}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="card border-info">
                                <div class="card-body">
                                    <h6 class="card-title text-info"><i class="ti ti-calendar me-2"></i>Booking Information</h6>
                                    <p><strong>Event:</strong> ${booking.event ? booking.event.title : 'N/A'}</p>
                                    <p><strong>Date:</strong> ${booking.date}</p>
                                    <p><strong>Time:</strong> ${booking.time || 'N/A'}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                // Add location information if available
                if (booking.selected_location) {
                    const location = booking.selected_location;
                    let locationDisplay = '';
                    let isClickable = false;
                    let clickAction = '';

                    if (location.type && location.value) {
                        switch(location.type) {
                            case 'zoom':
                                locationDisplay = `<i class="fas fa-video location-icon text-primary"></i>Zoom Meeting`;
                                isClickable = true;
                                clickAction = `onclick="window.open('${location.value}', '_blank')"`;
                                break;
                            case 'meet':
                                locationDisplay = `<i class="fab fa-google location-icon text-success"></i>Google Meet`;
                                isClickable = true;
                                clickAction = `onclick="window.open('${location.value}', '_blank')"`;
                                break;
                            case 'skype':
                                locationDisplay = `<i class="fab fa-skype location-icon text-info"></i>Skype`;
                                isClickable = true;
                                clickAction = `onclick="window.open('${location.value}', '_blank')"`;
                                break;
                            case 'phone':
                                locationDisplay = `<i class="fas fa-phone location-icon text-warning"></i>Phone: ${location.value}`;
                                isClickable = true;
                                clickAction = `onclick="window.open('tel:${location.value}', '_self')"`;
                                break;
                            case 'address':
                                locationDisplay = `<i class="fas fa-map-marker-alt location-icon text-danger"></i>${location.value}`;
                                isClickable = true;
                                clickAction = `onclick="window.open('https://maps.google.com/?q=${encodeURIComponent(location.value)}', '_blank')"`;
                                break;
                            case 'custom':
                                locationDisplay = `<i class="fas fa-map-marker-alt location-icon text-secondary"></i>${location.value}`;
                                break;
                            default:
                                locationDisplay = `<i class="fas fa-map-marker-alt location-icon text-secondary"></i>${location.display || location.value || 'Location specified'}`;
                        }
                    } else if (location.display) {
                        locationDisplay = `<i class="fas fa-map-marker-alt me-2 text-secondary"></i>${location.display}`;
                    }

                    if (locationDisplay) {
                        const cursorStyle = isClickable ? 'cursor: pointer;' : '';
                        const hoverStyle = isClickable ? 'text-decoration: underline;' : '';

                        content += `
                            <div class="row">
                                <div class="col-12 mb-3">
                                    <div class="card border-warning">
                                        <div class="card-body">
                                            <h6 class="card-title text-warning"><i class="ti ti-map-pin me-2"></i>Meeting Location</h6>
                                            <p class="mb-0">
                                                <span class="${isClickable ? 'clickable-location' : ''}"
                                                      ${isClickable ? clickAction : ''}>
                                                    ${locationDisplay}
                                                </span>
                                                ${isClickable ? '<span class="location-hint">(Click to open)</span>' : ''}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                    }
                }

                // Continue with the rest of the content

                // Add custom fields if available
                if (booking.custom_fields && booking.custom_fields_value) {
                    content += `
                        <div class="row">
                            <div class="col-12">
                                <div class="card border-success">
                                    <div class="card-body">
                                        <h6 class="card-title text-success"><i class="ti ti-forms me-2"></i>Additional Info</h6>
                                        <div class="row">
                    `;

                    // Process custom fields
                    const fields = Array.isArray(booking.custom_fields) ? booking.custom_fields : [];
                    const values = Array.isArray(booking.custom_fields_value) ? booking.custom_fields_value : [];

                    for(let i = 0; i < Math.min(fields.length, values.length); i++) {
                        const fieldType = fields[i];
                        const fieldValue = values[i];
                        const fieldLabel = getFieldLabel(fieldType);

                        content += `
                            <div class="col-md-6 mb-2">
                                <strong>${fieldLabel}:</strong> ${fieldValue || 'N/A'}
                            </div>
                        `;
                    }

                    content += `
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                }

                content += `
                    <div class="row">
                        <div class="col-12">
                            <div class="card border-secondary">
                                <div class="card-body">
                                    <h6 class="card-title text-secondary"><i class="ti ti-info-circle me-2"></i>System Information</h6>
                                    <p><strong>Booking ID:</strong> ${booking.id}</p>
                                    <p><strong>Created:</strong> ${new Date(booking.created_at).toLocaleString()}</p>
                                    <p><strong>Updated:</strong> ${new Date(booking.updated_at).toLocaleString()}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                $('#viewBookingContent').html(content);
                $('#viewBookingModal').modal('show');
            } else {
                showBookingsAlert('Error loading booking details: ' + response.message, 'danger');
            }
        },
        error: function(xhr) {
            showBookingsAlert('Error loading booking details', 'danger');
            console.error(xhr.responseText);
        }
    });
}

// Edit booking
function editBooking(bookingId) {
    // Load booking data
    $.ajax({
        url: `{{ url('bookings') }}/${bookingId}`,
        method: 'GET',
        success: function(response) {
            if (response.success) {
                const booking = response.data;

                // Populate form fields
                $('#edit_booking_id').val(booking.id);
                $('#edit_booking_name').val(booking.name);
                $('#edit_booking_email').val(booking.email);
                $('#edit_booking_phone').val(booking.phone || '');
                $('#edit_booking_date').val(booking.date);
                $('#edit_booking_time').val(booking.time || '');

                // Load events for dropdown
                loadEventsForEdit(booking.event_id);

                // Load custom fields
                loadCustomFieldsForEdit(booking);

                $('#editBookingModal').modal('show');
            } else {
                showBookingsAlert('Error loading booking details: ' + response.message, 'danger');
            }
        },
        error: function(xhr) {
            showBookingsAlert('Error loading booking details', 'danger');
            console.error(xhr.responseText);
        }
    });
}

// Load events for edit dropdown
function loadEventsForEdit(selectedEventId) {
    $.ajax({
        url: '{{ route("calendar-events.index") }}',
        method: 'GET',
        success: function(response) {
            if (response.success) {
                let options = '<option value="">{{ __("Select Event") }}</option>';
                response.data.forEach(function(event) {
                    const selected = event.id == selectedEventId ? 'selected' : '';
                    options += `<option value="${event.id}" ${selected}>${event.title}</option>`;
                });
                $('#edit_booking_event').html(options);
            }
        },
        error: function(xhr) {
            console.error('Error loading events:', xhr.responseText);
        }
    });
}

// Load custom fields for edit
function loadCustomFieldsForEdit(booking) {
    let customFieldsHtml = '';

    if (booking.custom_fields && booking.custom_fields_value) {
        const fields = Array.isArray(booking.custom_fields) ? booking.custom_fields : [];
        const values = Array.isArray(booking.custom_fields_value) ? booking.custom_fields_value : [];

        if (fields.length > 0) {
            customFieldsHtml += '<h6 class="mb-3"><i class="ti ti-forms me-2"></i>{{ __("Additional Info") }}</h6>';

            for(let i = 0; i < Math.min(fields.length, values.length); i++) {
                const fieldType = fields[i];
                const fieldValue = values[i];
                const fieldLabel = getFieldLabel(fieldType);

                customFieldsHtml += `
                    <div class="mb-3">
                        <label for="edit_custom_field_${i}" class="form-label">${fieldLabel}</label>
                        <input type="text" class="form-control" id="edit_custom_field_${i}"
                               name="custom_fields_value[${i}]" value="${fieldValue || ''}"
                               data-field-type="${fieldType}">
                    </div>
                `;
            }
        }
    }

    $('#edit_custom_fields_container').html(customFieldsHtml);
}

// Delete booking
function deleteBooking(bookingId) {
    // Create a more user-friendly confirmation dialog
    const confirmDelete = confirm('{{ __("Are you sure you want to delete this booking?") }}\n\n{{ __("This action cannot be undone and will permanently remove all booking data.") }}');

    if (confirmDelete) {
        $.ajax({
            url: `{{ url('bookings') }}/${bookingId}`,
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    showBookingsAlert('{{ __("Booking deleted successfully") }}', 'success');
                    loadBookings(); // Refresh the bookings list
                } else {
                    showBookingsAlert('Error: ' + response.message, 'danger');
                }
            },
            error: function(xhr) {
                showBookingsAlert('Error deleting booking', 'danger');
                console.error(xhr.responseText);
            }
        });
    }
}

// Handle edit booking form submission
$(document).on('submit', '#editBookingForm', function(e) {
    e.preventDefault();

    const bookingId = $('#edit_booking_id').val();
    const formData = {
        name: $('#edit_booking_name').val(),
        email: $('#edit_booking_email').val(),
        phone: $('#edit_booking_phone').val(),
        date: $('#edit_booking_date').val(),
        time: $('#edit_booking_time').val(),
        custom_fields_value: []
    };

    // Collect custom fields values
    $('#edit_custom_fields_container input[name^="custom_fields_value"]').each(function() {
        formData.custom_fields_value.push($(this).val());
    });

    $.ajax({
        url: `{{ url('bookings') }}/${bookingId}`,
        method: 'PUT',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        data: formData,
        success: function(response) {
            if (response.success) {
                showBookingsAlert('{{ __("Booking updated successfully") }}', 'success');
                $('#editBookingModal').modal('hide');
                loadBookings(); // Refresh the bookings list
            } else {
                showBookingsAlert('Error: ' + response.message, 'danger');
            }
        },
        error: function(xhr) {
            showBookingsAlert('Error updating booking', 'danger');
            console.error(xhr.responseText);
        }
    });
});

@endif

</script>

<!-- Custom Fields Modal -->
<div class="modal fade" id="customFieldsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="ti ti-forms me-2"></i>{{ __('Additional Info Details') }}
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body p-4" id="customFieldsContent">
                <!-- Custom fields will be displayed here -->
            </div>
            <div class="modal-footer bg-light">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="ti ti-x me-1"></i>{{ __('Close') }}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- View Booking Modal -->
@can('manage booking')
<div class="modal fade" id="viewBookingModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title">
                    <i class="ti ti-eye me-2"></i>{{ __('Appointment Details') }}
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="viewBookingContent">
                <!-- Booking details will be displayed here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="ti ti-x me-1"></i>{{ __('Close') }}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Booking Modal -->
<div class="modal fade" id="editBookingModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-warning text-white">
                <h5 class="modal-title">
                    <i class="ti ti-edit me-2"></i>{{ __('Edit Booking') }}
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form id="editBookingForm">
                <div class="modal-body">
                    <input type="hidden" id="edit_booking_id">

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit_booking_name" class="form-label">{{ __('Customer Name') }} *</label>
                            <input type="text" class="form-control" id="edit_booking_name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="edit_booking_email" class="form-label">{{ __('Email') }} *</label>
                            <input type="email" class="form-control" id="edit_booking_email" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit_booking_phone" class="form-label">{{ __('Phone') }}</label>
                            <input type="text" class="form-control" id="edit_booking_phone">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="edit_booking_event" class="form-label">{{ __('Event') }}</label>
                            <select class="form-control" id="edit_booking_event" disabled>
                                <option value="">{{ __('Loading events...') }}</option>
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit_booking_date" class="form-label">{{ __('Date') }} *</label>
                            <input type="date" class="form-control" id="edit_booking_date" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="edit_booking_time" class="form-label">{{ __('Time') }}</label>
                            <input type="time" class="form-control" id="edit_booking_time">
                        </div>
                    </div>

                    <div id="edit_custom_fields_container">
                        <!-- Custom fields will be populated here -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="ti ti-x me-1"></i>{{ __('Cancel') }}
                    </button>
                    <button type="submit" class="btn btn-warning">
                        <i class="ti ti-device-floppy me-1"></i>{{ __('Update Booking') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@endif

<!-- Location Modals -->
<!-- Zoom Modal -->
<div class="modal fade" id="zoomLocationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="ti ti-video me-2 text-primary"></i>{{ __('Edit Location') }}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <div class="d-flex align-items-center mb-3">
                        <i class="ti ti-video me-2 text-primary"></i>
                        <select class="form-select" id="zoom_type">
                            <option value="zoom">{{ __('Zoom') }}</option>
                        </select>
                    </div>
                    <input type="url" class="form-control" id="zoom_link" placeholder="https://zoom.us/j/123456789">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('CANCEL') }}</button>
                <button type="button" class="btn btn-success" onclick="saveLocation('zoom')">{{ __('UPDATE') }}</button>
            </div>
        </div>
    </div>
</div>

<!-- In-Person Modal -->
<div class="modal fade" id="in_personLocationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="ti ti-map-pin me-2 text-success"></i>{{ __('Edit Location') }}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <div class="d-flex align-items-center mb-3">
                        <i class="ti ti-map-pin me-2 text-success"></i>
                        <select class="form-select" id="in_person_type">
                            <option value="in_person">{{ __('In-person meeting') }}</option>
                        </select>
                    </div>
                    <textarea class="form-control" id="in_person_address" rows="3" placeholder="{{ __('Enter the physical address or location details') }}"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('CANCEL') }}</button>
                <button type="button" class="btn btn-success" onclick="saveLocation('in_person')">{{ __('UPDATE') }}</button>
            </div>
        </div>
    </div>
</div>

<!-- Phone Modal -->
<div class="modal fade" id="phoneLocationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="ti ti-phone me-2 text-info"></i>{{ __('Edit Location') }}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <div class="d-flex align-items-center mb-3">
                        <i class="ti ti-phone me-2 text-info"></i>
                        <select class="form-select" id="phone_type">
                            <option value="phone">{{ __('Phone call') }}</option>
                        </select>
                    </div>
                    <input type="tel" class="form-control" id="phone_number" placeholder="{{ __('Enter phone number or call details') }}">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('CANCEL') }}</button>
                <button type="button" class="btn btn-success" onclick="saveLocation('phone')">{{ __('UPDATE') }}</button>
            </div>
        </div>
    </div>
</div>

<!-- Google Meet Modal -->
<div class="modal fade" id="meetLocationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="ti ti-brand-google me-2 text-warning"></i>{{ __('Edit Location') }}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <div class="d-flex align-items-center mb-3">
                        <i class="ti ti-brand-google me-2 text-warning"></i>
                        <select class="form-select" id="meet_type">
                            <option value="meet">{{ __('Google Meet') }}</option>
                        </select>
                    </div>
                    <input type="url" class="form-control" id="meet_link" placeholder="https://meet.google.com/abc-defg-hij">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('CANCEL') }}</button>
                <button type="button" class="btn btn-success" onclick="saveLocation('meet')">{{ __('UPDATE') }}</button>
            </div>
        </div>
    </div>
</div>

<!-- Skype Modal -->
<div class="modal fade" id="skypeLocationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="ti ti-brand-skype me-2 text-primary"></i>{{ __('Edit Location') }}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <div class="d-flex align-items-center mb-3">
                        <i class="ti ti-brand-skype me-2 text-primary"></i>
                        <select class="form-select" id="skype_type">
                            <option value="skype">{{ __('Skype') }}</option>
                        </select>
                    </div>
                    <input type="text" class="form-control" id="skype_link" placeholder="{{ __('Skype ID or meeting link') }}">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('CANCEL') }}</button>
                <button type="button" class="btn btn-success" onclick="saveLocation('skype')">{{ __('UPDATE') }}</button>
            </div>
        </div>
    </div>
</div>

<!-- Others Modal -->
<div class="modal fade" id="othersLocationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="ti ti-dots me-2 text-secondary"></i>{{ __('Edit Location') }}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <div class="d-flex align-items-center mb-3">
                        <i class="ti ti-dots me-2 text-secondary"></i>
                        <select class="form-select" id="others_type">
                            <option value="others">{{ __('Others') }}</option>
                        </select>
                    </div>
                    <textarea class="form-control" id="others_details" rows="3" placeholder="{{ __('Enter custom location details') }}"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('CANCEL') }}</button>
                <button type="button" class="btn btn-success" onclick="saveLocation('others')">{{ __('UPDATE') }}</button>
            </div>
        </div>
    </div>
</div>
@endpush